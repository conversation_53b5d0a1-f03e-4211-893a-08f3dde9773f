// <PERSON>OD<PERSON> was here
import { bootstrapData } from 'src/preamble'; // DODO added 44120742
import { Charts, Layout, LayoutItem } from 'src/dashboard/types';
import {
  CHART_TYPE,
  DASHBOARD_ROOT_TYPE,
  TAB_TYPE,
} from 'src/dashboard/util/componentTypes';
import { DASHBOARD_ROOT_ID } from 'src/dashboard/util/constants';
import { logging, NativeFilterScope, t } from '@superset-ui/core';
import { BuildTreeLeafTitle, TreeItem } from './types';

// DODO added start 44120742
const locale = bootstrapData?.common?.locale || 'en';
const localisedSliceNameOverrideField =
  locale === 'en' ? 'sliceNameOverride' : 'sliceNameOverrideRU';
const localisedSliceNameField = locale === 'en' ? 'sliceName' : 'sliceNameRU';
// DODO added stop 44120742

export const isShowTypeInTree = ({ type }: LayoutItem) =>
  type === TAB_TYPE || type === CHART_TYPE || type === DASHBOARD_ROOT_TYPE;

export const getNodeTitle = (node: LayoutItem) =>
  node?.meta?.[localisedSliceNameOverrideField] ?? // DODO added 44120742
  node?.meta?.[localisedSliceNameField] ?? // DODO added 44120742
  node?.meta?.sliceNameOverride ??
  node?.meta?.sliceNameOverrideRU ?? // DODO added 44120742
  node?.meta?.sliceName ??
  node?.meta?.sliceNameRU ?? // DODO added 44120742
  node?.meta?.text ??
  node?.meta?.defaultText ??
  node?.id?.toString?.() ??
  '';

export const buildTree = (
  node: LayoutItem,
  treeItem: TreeItem,
  layout: Layout,
  charts: Charts,
  validNodes: string[],
  initiallyExcludedCharts: number[],
  buildTreeLeafTitle: BuildTreeLeafTitle,
) => {
  let itemToPass: TreeItem = treeItem;
  if (
    node &&
    treeItem &&
    isShowTypeInTree(node) &&
    node.type !== DASHBOARD_ROOT_TYPE &&
    validNodes?.includes?.(node.id)
  ) {
    const title = buildTreeLeafTitle(
      getNodeTitle(node),
      initiallyExcludedCharts?.includes?.(node.meta?.chartId),
      t(
        "This chart might be incompatible with the filter (datasets don't match)",
      ),
    );

    const currentTreeItem = {
      key: node.id,
      title,
      children: [],
    };
    treeItem.children.push(currentTreeItem);
    itemToPass = currentTreeItem;
  }
  node?.children?.forEach?.(child => {
    const node = layout?.[child];
    if (node) {
      buildTree(
        node,
        itemToPass,
        layout,
        charts,
        validNodes,
        initiallyExcludedCharts,
        buildTreeLeafTitle,
      );
    } else {
      logging.warn(
        `Unable to find item with id: ${child} in the dashboard layout. This may indicate you have invalid references in your dashboard and the references to id: ${child} should be removed.`,
      );
    }
  });
};

const addInvisibleParents = (layout: Layout, item: string) => [
  ...(layout[item]?.children || []),
  ...Object.values(layout)
    .filter(
      val =>
        val.parents &&
        val.parents[val.parents.length - 1] === item &&
        !isShowTypeInTree(layout[val.parents[val.parents.length - 1]]),
    )
    .map(({ id }) => id),
];

// Generate checked options for Ant tree from redux scope
const checkTreeItem = (
  checkedItems: string[],
  layout: Layout,
  items: string[],
  excluded: number[],
) => {
  items.forEach(item => {
    checkTreeItem(
      checkedItems,
      layout,
      addInvisibleParents(layout, item),
      excluded,
    );
    if (
      layout[item]?.type === CHART_TYPE &&
      !excluded.includes(layout[item]?.meta.chartId)
    ) {
      checkedItems.push(item);
    }
  });
};

export const getTreeCheckedItems = (
  scope: NativeFilterScope,
  layout: Layout,
) => {
  const checkedItems: string[] = [];
  checkTreeItem(checkedItems, layout, [...scope.rootPath], [...scope.excluded]);
  return [...new Set(checkedItems)];
};

// Looking for first common parent for selected charts/tabs/tab
export const findFilterScope = (
  checkedKeys: string[],
  layout: Layout,
): NativeFilterScope => {
  if (!checkedKeys.length) {
    return {
      rootPath: [],
      excluded: [],
    };
  }

  // Get arrays of parents for selected charts
  const checkedItemParents = checkedKeys
    .filter(item => layout[item]?.type === CHART_TYPE)
    .map(key => {
      const parents = [DASHBOARD_ROOT_ID, ...(layout[key]?.parents || [])];
      return parents.filter(parent => isShowTypeInTree(layout[parent]));
    });
  // Sort arrays of parents to get first shortest array of parents,
  // that means on it's level of parents located common parent, from this place parents start be different
  checkedItemParents.sort((p1, p2) => p1.length - p2.length);
  const rootPath = checkedItemParents.map(
    parents => parents[checkedItemParents[0].length - 1],
  );

  const excluded: number[] = [];
  const isExcluded = (parent: string, item: string) =>
    rootPath.includes(parent) && !checkedKeys.includes(item);
  // looking for charts to be excluded: iterate over all charts
  // and looking for charts that have one of their parents in `rootPath` and not in selected items
  Object.entries(layout).forEach(([key, value]) => {
    const parents = value.parents || [];
    if (
      value.type === CHART_TYPE &&
      [DASHBOARD_ROOT_ID, ...parents]?.find(parent => isExcluded(parent, key))
    ) {
      excluded.push(value.meta.chartId);
    }
  });

  return {
    rootPath: [...new Set(rootPath)],
    excluded,
  };
};

export const getDefaultScopeValue = (
  chartId?: number,
  initiallyExcludedCharts: number[] = [],
): NativeFilterScope => ({
  rootPath: [DASHBOARD_ROOT_ID],
  excluded: chartId
    ? [chartId, ...initiallyExcludedCharts]
    : initiallyExcludedCharts,
});

export const isScopingAll = (scope: NativeFilterScope, chartId?: number) =>
  !scope ||
  (scope.rootPath[0] === DASHBOARD_ROOT_ID &&
    !scope.excluded.filter(item => item !== chartId).length);
