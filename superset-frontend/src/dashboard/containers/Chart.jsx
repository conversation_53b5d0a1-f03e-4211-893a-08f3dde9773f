// DODO was here
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { bootstrapData } from 'src/preamble'; // DODO added 44120742
import {
  toggleExpandSlice,
  setFocusedFilterField,
  unsetFocusedFilterField,
} from 'src/dashboard/actions/dashboardState';
import { updateComponents } from 'src/dashboard/actions/dashboardLayout';
import { changeFilter } from 'src/dashboard/actions/dashboardFilters';
import {
  addSuccessToast,
  addDangerToast,
} from 'src/components/MessageToasts/actions';
import { refreshChart } from 'src/components/Chart/chartAction';
import { logEvent } from 'src/logger/actions';
import {
  getActiveFilters,
  getAppliedFilterValues,
} from 'src/dashboard/util/activeDashboardFilters';
import getFormDataWithExtraFilters from 'src/dashboard/util/charts/getFormDataWithExtraFilters';
import Chart from 'src/dashboard/components/gridComponents/Chart';
import { PLACEHOLDER_DATASOURCE } from 'src/dashboard/constants';
import { enforceSharedLabelsColorsArray } from 'src/utils/colorScheme';

const locale = bootstrapData?.common?.locale || 'en'; // DODO added 44120742

const EMPTY_OBJECT = {};

function mapStateToProps(
  {
    charts: chartQueries,
    dashboardInfo,
    dashboardState,
    dataMask,
    datasources,
    sliceEntities,
    nativeFilters,
    common,
  },
  ownProps,
) {
  const { id, extraControls, setControlValue, toggleIsExportingData } =
    ownProps;
  const chart = chartQueries[id] || EMPTY_OBJECT;
  const datasource =
    (chart && chart.form_data && datasources[chart.form_data.datasource]) ||
    PLACEHOLDER_DATASOURCE;
  const {
    colorScheme: appliedColorScheme,
    colorNamespace,
    datasetsStatus,
  } = dashboardState;
  const labelsColor = dashboardInfo?.metadata?.label_colors || {};
  const labelsColorMap = dashboardInfo?.metadata?.map_label_colors || {};
  const sharedLabelsColors = enforceSharedLabelsColorsArray(
    dashboardInfo?.metadata?.shared_label_colors,
  );
  const ownColorScheme = chart.form_data?.color_scheme;
  // note: this method caches filters if possible to prevent render cascades
  const formData = getFormDataWithExtraFilters({
    chart,
    chartConfiguration: dashboardInfo.metadata?.chart_configuration,
    charts: chartQueries,
    filters: getAppliedFilterValues(id),
    colorNamespace,
    colorScheme: appliedColorScheme,
    ownColorScheme,
    sliceId: id,
    nativeFilters: nativeFilters?.filters,
    allSliceIds: dashboardState.sliceIds,
    dataMask,
    extraControls,
    labelsColor,
    labelsColorMap,
    sharedLabelsColors,
    locale, // DODO added 44211759
  });

  formData.dashboardId = dashboardInfo.id;

  return {
    chart,
    datasource,
    labelsColor,
    labelsColorMap,
    slice: sliceEntities.slices[id],
    timeout: dashboardInfo.common.conf.SUPERSET_WEBSERVER_TIMEOUT,
    filters: getActiveFilters() || EMPTY_OBJECT,
    formData,
    editMode: dashboardState.editMode,
    isExpanded: !!dashboardState.expandedSlices[id],
    supersetCanExplore: !!dashboardInfo.superset_can_explore,
    supersetCanShare: !!dashboardInfo.superset_can_share,
    supersetCanCSV: !!dashboardInfo.superset_can_csv,
    ownState: dataMask[id]?.ownState,
    filterState: dataMask[id]?.filterState,
    maxRows: common.conf.SQL_MAX_ROW,
    setControlValue,
    datasetsStatus,
    emitCrossFilters: !!dashboardInfo.crossFiltersEnabled,
    locale, // DODO added 44120742
    toggleIsExportingData, // DODO added 48951211
  };
}

function mapDispatchToProps(dispatch) {
  return bindActionCreators(
    {
      updateComponents,
      addSuccessToast,
      addDangerToast,
      toggleExpandSlice,
      changeFilter,
      setFocusedFilterField,
      unsetFocusedFilterField,
      refreshChart,
      logEvent,
    },
    dispatch,
  );
}

export default connect(mapStateToProps, mapDispatchToProps)(Chart);
