// DODO was here
import { GenericDataType, t, validateNumber } from '@superset-ui/core';
import {
  AGGREGATE_FUNCTION_OPTIONS, // DODO added 45525377
  ControlFormItemSpec,
  D3_FORMAT_DOCS,
  D3_FORMAT_OPTIONS,
  D3_TIME_FORMAT_DOCS,
  D3_TIME_FORMAT_OPTIONS,
} from '@superset-ui/chart-controls';
import Icons from 'src/components/Icons';
import { ColumnConfigFormLayout } from './types';

export type SharedColumnConfigProp =
  | 'alignPositiveNegative'
  | 'colorPositiveNegative'
  | 'columnWidth'
  | 'fractionDigits'
  | 'd3NumberFormat'
  | 'd3SmallNumberFormat'
  | 'd3TimeFormat'
  | 'horizontalAlign'
  | 'truncateLongCells'
  | 'showCellBars'
  | 'currencyFormat'
  | 'exportAsTime' // DODO added 44136746
  // DOD<PERSON> added start 45525377
  | 'pinColumn'
  | 'aggregation'
  | 'hideValueInTotal';
// DODO added stop 45525377

const d3NumberFormat: ControlFormItemSpec<'Select'> = {
  allowNewOptions: true,
  controlType: 'Select',
  label: t('D3 format'),
  description: D3_FORMAT_DOCS,
  // options: D3_FORMAT_OPTIONS.map(option => ({
  // DODO changed 44211769
  options: [['', t('Default')], ...D3_FORMAT_OPTIONS].map(option => ({
    value: option[0],
    label: option[1],
  })),
  // defaultValue: D3_FORMAT_OPTIONS[0][0],
  // DODO changed 44211769
  defaultValue: '',
  creatable: true,
  minWidth: '14em',
  debounceDelay: 500,
};

const d3TimeFormat: ControlFormItemSpec<'Select'> = {
  controlType: 'Select',
  label: t('D3 format'),
  description: D3_TIME_FORMAT_DOCS,
  options: D3_TIME_FORMAT_OPTIONS.map(option => ({
    value: option[0],
    label: option[1],
  })),
  defaultValue: D3_TIME_FORMAT_OPTIONS[0][0],
  creatable: true,
  minWidth: '10em',
  debounceDelay: 500,
};

const fractionDigits: ControlFormItemSpec<'Slider'> = {
  controlType: 'Slider',
  label: t('Fraction digits'),
  description: t('Number of decimal digits to round numbers to'),
  min: 0,
  step: 1,
  max: 100,
  defaultValue: 100,
};

const columnWidth: ControlFormItemSpec<'InputNumber'> = {
  controlType: 'InputNumber',
  label: t('Min Width'),
  description: t(
    "Default minimal column width in pixels, actual width may still be larger than this if other columns don't need much space",
  ),
  width: 120,
  placeholder: t('auto'),
  debounceDelay: 400,
  validators: [validateNumber],
};

const horizontalAlign: ControlFormItemSpec<'RadioButtonControl'> & {
  value?: 'left' | 'right' | 'center';
  defaultValue?: 'left' | 'right' | 'center';
} = {
  controlType: 'RadioButtonControl',
  label: t('Text align'),
  description: t('Horizontal alignment'),
  width: 130,
  debounceDelay: 50,
  defaultValue: 'left',
  options: [
    ['left', <Icons.AlignLeftOutlined iconSize="m" />],
    ['center', <Icons.AlignCenterOutlined iconSize="m" />],
    ['right', <Icons.AlignRightOutlined iconSize="m" />],
  ],
};

const showCellBars: ControlFormItemSpec<'Checkbox'> = {
  controlType: 'Checkbox',
  label: t('Show cell bars'),
  description: t('Whether to display a bar chart background in table columns'),
  defaultValue: true,
  debounceDelay: 200,
};

const alignPositiveNegative: ControlFormItemSpec<'Checkbox'> = {
  controlType: 'Checkbox',
  label: t('Align +/-'),
  description: t(
    'Whether to align positive and negative values in cell bar chart at 0',
  ),
  defaultValue: false,
  debounceDelay: 200,
};

const colorPositiveNegative: ControlFormItemSpec<'Checkbox'> = {
  controlType: 'Checkbox',
  label: t('Color +/-'),
  description: t(
    'Whether to colorize numeric values by if they are positive or negative',
  ),
  defaultValue: false,
  debounceDelay: 200,
};

const truncateLongCells: ControlFormItemSpec<'Checkbox'> = {
  controlType: 'Checkbox',
  label: t('Truncate Cells'),
  description: t('Truncate long cells to the "min width" set above'),
  defaultValue: false,
  debounceDelay: 400,
};

// DODO added start 45525377
const pinColumn: ControlFormItemSpec<'Checkbox'> = {
  controlType: 'Checkbox',
  label: t('Pin column'),
  description: t('Pin column with horizontal scroll'),
  defaultValue: false,
  debounceDelay: 400,
};

const aggregation: ControlFormItemSpec<'Select'> = {
  controlType: 'Select',
  label: t('Select aggregate options'),
  placeholder: t('%s aggregates(s)', AGGREGATE_FUNCTION_OPTIONS.length),
  description: t('Select aggregate options'),
  options: [['', t('Default')], ...AGGREGATE_FUNCTION_OPTIONS].map(option => ({
    value: option[0],
    label: option[1],
  })),
  defaultValue: '',
  minWidth: '10em',
  debounceDelay: 500,
};

const hideValueInTotal: ControlFormItemSpec<'Checkbox'> = {
  controlType: 'Checkbox',
  label: t('Hide value in Total'),
  description: t('Hide value in Total'),
  defaultValue: false,
  debounceDelay: 400,
};
// DODO added stop 45525377

const currencyFormat: ControlFormItemSpec<'CurrencyControl'> = {
  controlType: 'CurrencyControl',
  label: t('Currency format'),
  description: t(
    'Customize chart metrics or columns with currency symbols as prefixes or suffixes. Choose a symbol from dropdown or type your own.',
  ),
  debounceDelay: 200,
};

// DODO added 44136746
const exportAsTime: ControlFormItemSpec<'Checkbox'> = {
  controlType: 'Checkbox',
  label: t('Export as time'),
  description: t('Export a numeric value as number of days'),
  defaultValue: false,
  debounceDelay: 400,
};
/**
 * All configurable column formatting properties.
 */
export const SHARED_COLUMN_CONFIG_PROPS = {
  d3NumberFormat,
  d3SmallNumberFormat: {
    ...d3NumberFormat,
    label: t('Small number format'),
    description: t(
      'D3 number format for numbers between -1.0 and 1.0, ' +
        'useful when you want to have different significant digits for small and large numbers',
    ),
  },
  d3TimeFormat,
  fractionDigits,
  columnWidth,
  truncateLongCells,
  horizontalAlign,
  showCellBars,
  alignPositiveNegative,
  colorPositiveNegative,
  currencyFormat,
  exportAsTime, // DODO added 44136746
  // DODO added start 45525377
  pinColumn,
  aggregation,
  hideValueInTotal,
  // DODO added stop 45525377
};

export const DEFAULT_CONFIG_FORM_LAYOUT: ColumnConfigFormLayout = {
  [GenericDataType.String]: [
    [
      'columnWidth',
      { name: 'horizontalAlign', override: { defaultValue: 'left' } },
    ],
    ['truncateLongCells'],
  ],
  [GenericDataType.Numeric]: [
    {
      tab: t('Display'),
      children: [
        [
          'columnWidth',
          { name: 'horizontalAlign', override: { defaultValue: 'right' } },
        ],
        ['showCellBars'],
        ['alignPositiveNegative'],
        ['colorPositiveNegative'],
      ],
    },
    {
      tab: t('Number formatting'),
      children: [
        ['d3NumberFormat'],
        ['d3SmallNumberFormat'],
        ['currencyFormat'],
      ],
    },
  ],
  [GenericDataType.Temporal]: [
    [
      'columnWidth',
      { name: 'horizontalAlign', override: { defaultValue: 'left' } },
    ],
    ['d3TimeFormat'],
  ],
  [GenericDataType.Boolean]: [
    [
      'columnWidth',
      { name: 'horizontalAlign', override: { defaultValue: 'left' } },
    ],
  ],
};
