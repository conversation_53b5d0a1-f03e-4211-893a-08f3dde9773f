{"domain": "superset", "locale_data": {"superset": {"22": [""], "": {"domain": "superset", "plural_forms": "nplurals=2; plural=(n != 1);", "lang": "en"}, "The datasource is too large to query.": [""], "The database is under an unusual load.": [""], "The database returned an unexpected error.": [""], "There is a syntax error in the SQL query. Perhaps there was a misspelling or a typo.": [""], "The column was deleted or renamed in the database.": [""], "The table was deleted or renamed in the database.": [""], "One or more parameters specified in the query are missing.": [""], "The hostname provided can't be resolved.": [""], "The port is closed.": [""], "The host might be down, and can't be reached on the provided port.": [""], "Superset encountered an error while running a command.": [""], "Superset encountered an unexpected error.": [""], "The username provided when connecting to a database is not valid.": [""], "The password provided when connecting to a database is not valid.": [""], "Either the username or the password is wrong.": [""], "Either the database is spelled incorrectly or does not exist.": [""], "The schema was deleted or renamed in the database.": [""], "User doesn't have the proper permissions.": [""], "One or more parameters needed to configure a database are missing.": [""], "The submitted payload has the incorrect format.": [""], "The submitted payload has the incorrect schema.": [""], "Results backend needed for asynchronous queries is not configured.": [""], "Database does not allow data manipulation.": [""], "The CTAS (create table as select) doesn't have a SELECT statement at the end. Please make sure your query has a SELECT as its last statement. Then, try running your query again.": [""], "CVAS (create view as select) query has more than one statement.": [""], "CVAS (create view as select) query is not a SELECT statement.": [""], "Query is too complex and takes too long to run.": [""], "The database is currently running too many queries.": [""], "One or more parameters specified in the query are malformed.": [""], "The object does not exist in the given database.": [""], "The query has a syntax error.": [""], "The results backend no longer has the data from the query.": [""], "The query associated with the results was deleted.": [""], "The results stored in the backend were stored in a different format, and no longer can be deserialized.": [""], "The port number is invalid.": [""], "Failed to start remote query on a worker.": [""], "The database was deleted.": [""], "Custom SQL fields cannot contain sub-queries.": [""], "The submitted payload failed validation.": [""], "Invalid certificate": [""], "The schema of the submitted payload is invalid.": [""], "Error parsing": [""], " near '%(highlight)s'": [""], " at line %(line)d": [""], ":%(column)d": [""], "Unsafe return type for function %(func)s: %(value_type)s": [""], "Unsupported return value for method %(name)s": [""], "Unsafe template value for key %(key)s: %(value_type)s": [""], "Unsupported template value for key %(key)s": [""], "Please specify the Dataset ID for the ``%(name)s`` metric in the Jinja macro.": [""], "Metric ``%(metric_name)s`` not found in %(dataset_name)s.": [""], "Viz is missing a datasource": [""], "Applied rolling window did not return any data. Please make sure the source query satisfies the minimum periods defined in the rolling window.": [""], "From date cannot be larger than to date": [""], "Cached value not found": [""], "Columns missing in datasource: %(invalid_columns)s": [""], "Time Table View": [""], "Pick at least one metric": [""], "When using 'Group By' you are limited to use a single metric": [""], "Calendar Heatmap": [""], "Bubble Chart": [""], "Please use 3 different metric labels": [""], "Pick a metric for x, y and size": [""], "Bullet Chart": [""], "Pick a metric to display": [""], "Time Series - Line Chart": [""], "An enclosed time range (both start and end) must be specified when using a Time Comparison.": [""], "Time Series - Bar Chart": [""], "Time Series - Period Pivot": [""], "Time Series - Percent Change": [""], "Time Series - Stacked": [""], "Histogram": [""], "Must have at least one numeric column specified": [""], "Distribution - Bar Chart": [""], "Can't have overlap between Series and Breakdowns": [""], "Pick at least one field for [Series]": [""], "Sankey": [""], "Pick exactly 2 columns as [Source / Target]": [""], "There's a loop in your Sankey, please provide a tree. Here's a faulty link: {}": [""], "Directed Force Layout": [""], "Country Map": [""], "World Map": [""], "Parallel Coordinates": [""], "Heatmap": [""], "Horizon Charts": [""], "Mapbox": [""], "[Longitude] and [Latitude] must be set": [""], "Must have a [Group By] column to have 'count' as the [Label]": [""], "Choice of [Label] must be present in [Group By]": [""], "Choice of [Point Radius] must be present in [Group By]": [""], "[Longitude] and [Latitude] columns must be present in [Group By]": [""], "Deck.gl - Multiple Layers": [""], "Bad spatial key": [""], "Invalid spatial point encountered: %(latlong)s": [""], "Encountered invalid NULL spatial entry,                                        please consider filtering those out": [""], "Deck.gl - Scatter plot": [""], "Deck.gl - Screen Grid": [""], "Deck.gl - 3D Grid": [""], "Deck.gl - Paths": [""], "Deck.gl - Polygon": [""], "Deck.gl - 3D HEX": [""], "Deck.gl - Heatmap": [""], "Deck.gl - Contour": [""], "Deck.gl - GeoJSON": [""], "Deck.gl - Arc": [""], "Event flow": [""], "Time Series - Paired t-test": [""], "Time Series - Nightingale Rose Chart": [""], "Partition Diagram": [""], "Please choose at least one groupby": [""], "Invalid advanced data type: %(advanced_data_type)s": [""], "Deleted %(num)d annotation layer": ["", "Deleted %(num)d annotation layers"], "All Text": [""], "Deleted %(num)d annotation": ["", "Deleted %(num)d annotations"], "Deleted %(num)d chart": ["", "Deleted %(num)d charts"], "Is certified": [""], "Has created by": [""], "Created by me": [""], "Owned Created or Favored": [""], "`confidence_interval` must be between 0 and 1 (exclusive)": [""], "lower percentile must be greater than 0 and less than 100. Must be lower than upper percentile.": [""], "upper percentile must be greater than 0 and less than 100. Must be higher than lower percentile.": [""], "`width` must be greater or equal to 0": [""], "`row_limit` must be greater than or equal to 0": [""], "`row_offset` must be greater than or equal to 0": [""], "orderby column must be populated": [""], "Chart has no query context saved. Please save the chart again.": [""], "Request is incorrect: %(error)s": [""], "Request is not JSON": [""], "Empty query result": [""], "Owners are invalid": [""], "Some roles do not exist": [""], "Datasource type is invalid": [""], "Datasource does not exist": [""], "Query does not exist": [""], "Annotation layer parameters are invalid.": [""], "Annotation layer could not be created.": [""], "Annotation layer could not be updated.": [""], "Annotation layer not found.": [""], "Annotation layers could not be deleted.": [""], "Annotation layer has associated annotations.": [""], "Name must be unique": [""], "End date must be after start date": [""], "Short description must be unique for this layer": [""], "Annotation not found.": [""], "Annotation parameters are invalid.": [""], "Annotation could not be created.": [""], "Annotation could not be updated.": [""], "Annotations could not be deleted.": [""], "There are associated alerts or reports: %(report_names)s": [""], "Time string is ambiguous. Please specify [%(human_readable)s ago] or [%(human_readable)s later].": [""], "Cannot parse time string [%(human_readable)s]": [""], "Time delta is ambiguous. Please specify [%(human_readable)s ago] or [%(human_readable)s later].": [""], "Database does not exist": [""], "Dashboards do not exist": [""], "Datasource type is required when datasource_id is given": [""], "Chart parameters are invalid.": [""], "Chart could not be created.": [""], "Chart could not be updated.": [""], "Charts could not be deleted.": [""], "There are associated alerts or reports": [""], "You don't have access to this chart.": [""], "Changing this chart is forbidden": [""], "Import chart failed for an unknown reason": [""], "Changing one or more of these dashboards is forbidden": [""], "Chart not found": [""], "Error faving chart": [""], "Error unfaving chart": [""], "Error: %(error)s": [""], "CSS templates could not be deleted.": [""], "CSS template not found.": [""], "Must be unique": [""], "Dashboard parameters are invalid.": [""], "Dashboards could not be created.": [""], "Dashboard could not be updated.": [""], "Dashboard could not be deleted.": [""], "Embedded dashboard could not be deleted.": [""], "Changing this Dashboard is forbidden": [""], "Import dashboard failed for an unknown reason": [""], "You don't have access to this dashboard.": [""], "Dashboard cannot be copied due to invalid parameters.": [""], "Dashboard cannot be favorited.": [""], "Dashboard cannot be unfavorited.": [""], "You don't have access to this embedded dashboard config.": [""], "No data in file": [""], "Database parameters are invalid.": [""], "A database with the same name already exists.": [""], "Field is required": [""], "Field cannot be decoded by JSON. %(json_error)s": [""], "The metadata_params in Extra field is not configured correctly. The key %{key}s is invalid.": [""], "Database not found.": [""], "Database schema is not allowed for csv uploads.": [""], "Database type does not support file uploads.": [""], "Database upload file failed": [""], "Database upload file failed, while saving metadata": [""], "Database could not be created.": [""], "Database could not be updated.": [""], "Connection failed, please check your connection settings": [""], "Cannot delete a database that has datasets attached": [""], "Database could not be deleted.": [""], "Stopped an unsafe database connection": [""], "Could not load database driver": [""], "Unexpected error occurred, please check your logs for details": [""], "no SQL validator is configured": [""], "No validator found (configured for the engine)": [""], "Was unable to check your query": [""], "An unexpected error occurred": [""], "Import database failed for an unknown reason": [""], "Could not load database driver: {}": [""], "SSH Tunnel could not be deleted.": [""], "SSH Tunnel not found.": [""], "SSH Tunnel parameters are invalid.": [""], "A database port is required when connecting via SSH Tunnel.": [""], "SSH Tunnel could not be updated.": [""], "Creating SSH Tunnel failed for an unknown reason": [""], "SSH Tunneling is not enabled": [""], "Must provide credentials for the SSH Tunnel": [""], "Cannot have multiple credentials for the SSH Tunnel": [""], "Table already exists. You can change your 'if table already exists' strategy to append or replace or provide a different Table Name to use.": [""], "Parsing error: %(error)s": [""], "Error reading Columnar file": [""], "Unexpected no file extension found": [""], "Not a valid ZIP file": [""], "ZIP file contains multiple file types": [""], "Error reading CSV file": [""], "Error reading Excel file": [""], "Excel file format cannot be determined": [""], "Dataset %(table)s already exists": [""], "Database not allowed to change": [""], "One or more columns do not exist": [""], "One or more columns are duplicated": [""], "One or more columns already exist": [""], "One or more metrics do not exist": [""], "One or more metrics are duplicated": [""], "One or more metrics already exist": [""], "Table [%(table)s] could not be found, please double check your database connection, schema, and table name": [""], "Dataset does not exist": [""], "Dataset parameters are invalid.": [""], "Dataset could not be created.": [""], "Dataset could not be updated.": [""], "Datasets could not be deleted.": [""], "Samples for dataset could not be retrieved.": [""], "Changing this dataset is forbidden": [""], "Import dataset failed for an unknown reason": [""], "You don't have access to this dataset.": [""], "Dataset could not be duplicated.": [""], "Data URI is not allowed.": [""], "The provided table was not found in the provided database": [""], "Dataset column not found.": [""], "Dataset column delete failed.": [""], "Changing this dataset is forbidden.": [""], "Dataset metric not found.": [""], "Dataset metric delete failed.": [""], "Form data not found in cache, reverting to chart metadata.": [""], "Form data not found in cache, reverting to dataset metadata.": [""], "[Missing Dataset]": [""], "Onboarding parameters are invalid.": [""], "Onboarding could not be updated.": [""], "Changing this Onboarding is forbidden": [""], "You don't have access to this Onboarding.": [""], "Saved queries could not be deleted.": [""], "Saved query not found.": [""], "Import saved query failed for an unknown reason.": [""], "Saved query parameters are invalid.": [""], "Alert query returned more than one row. %(num_rows)s rows returned": [""], "Alert query returned more than one column. %(num_cols)s columns returned": [""], "An error occurred when running alert query": [""], "Invalid tab ids: %s(tab_ids)": [""], "Dashboard does not exist": [""], "Chart does not exist": [""], "Database is required for alerts": [""], "Type is required": [""], "Choose a chart or dashboard not both": [""], "Must choose either a chart or a dashboard": [""], "%(report_type)s schedule frequency exceeding limit. Please configure a schedule with a minimum interval of %(minimum_interval)d minutes per execution.": [""], "Please save your chart first, then try creating a new email report.": [""], "Please save your dashboard first, then try creating a new email report.": [""], "Report Schedule parameters are invalid.": [""], "Report Schedule could not be created.": [""], "Report Schedule could not be updated.": [""], "Report Schedule not found.": [""], "Report Schedule delete failed.": [""], "Report Schedule log prune failed.": [""], "Report Schedule execution failed when generating a screenshot.": [""], "Report Schedule execution failed when generating a pdf.": [""], "Report Schedule execution failed when generating a csv.": [""], "Report Schedule execution failed when generating a dataframe.": [""], "Report Schedule execution got an unexpected error.": [""], "Report Schedule is still working, refusing to re-compute.": [""], "Report Schedule reached a working timeout.": [""], "A report named \"%(name)s\" already exists": [""], "An alert named \"%(name)s\" already exists": [""], "Resource already has an attached report.": [""], "Alert query returned more than one row.": [""], "Alert validator config error.": [""], "Alert query returned more than one column.": [""], "Alert query returned a non-number value.": [""], "Alert found an error while executing a query.": [""], "A timeout occurred while executing the query.": [""], "A timeout occurred while taking a screenshot.": [""], "A timeout occurred while generating a csv.": [""], "A timeout occurred while generating a dataframe.": [""], "Alert fired during grace period.": [""], "Alert ended grace period.": [""], "Alert on grace period": [""], "Report Schedule state not found": [""], "Report schedule system error": [""], "Report schedule client error": [""], "Report schedule unexpected error": [""], "Changing this report is forbidden": [""], "An error occurred while pruning logs ": [""], "RLS Rule not found.": [""], "RLS rules could not be deleted.": [""], "Statement parameters are invalid.": [""], "Statements could not be created.": [""], "Statement could not be updated.": [""], "Changing this Statement is forbidden": [""], "You don't have access to this Statement.": [""], "Tag parameters are invalid.": [""], "Tag could not be created.": [""], "Tag could not be updated.": [""], "Tag could not be deleted.": [""], "Tagged Object could not be deleted.": [""], "Team parameters are invalid.": [""], "Teams could not be created.": [""], "Team could not be updated.": [""], "Team could not be deleted.": [""], "Changing this Team is forbidden": [""], "You don't have access to this Team.": [""], "An error occurred while creating the value.": [""], "An error occurred while accessing the value.": [""], "An error occurred while deleting the value.": [""], "An error occurred while updating the value.": [""], "You don't have permission to modify the value.": [""], "Resource was not found.": [""], "Invalid result type: %(result_type)s": [""], "Columns missing in dataset: %(invalid_columns)s": [""], "Time Grain must be specified when using Time Shift.": [""], "The chart does not exist": [""], "The chart datasource does not exist": [""], "The chart query context does not exist": [""], "Duplicate column/metric labels: %(labels)s. Please make sure all columns and metrics have a unique label.": [""], "The following entries in `series_columns` are missing in `columns`: %(columns)s. ": [""], "`operation` property of post processing object undefined": [""], "Unsupported post processing operation: %(operation)s": [""], "Error in jinja expression in RLS filters: %(msg)s": [""], "Error in jinja expression in fetch values predicate: %(msg)s": [""], "Virtual dataset query must be read-only": [""], "Metric '%(metric)s' does not exist": [""], "Db engine did not return all queried columns": [""], "Virtual dataset query cannot be empty": [""], "Only `SELECT` statements are allowed": [""], "Only single queries supported": [""], "Columns": [""], "Show Column": [""], "Add Column": [""], "Edit Column": [""], "Whether to make this column available as a [Time Granularity] option, column has to be DATETIME or DATETIME-like": [""], "Whether this column is exposed in the `Filters` section of the explore view.": [""], "The data type that was inferred by the database. It may be necessary to input a type manually for expression-defined columns in some cases. In most case users should not need to alter this.": [""], "Column": [""], "Verbose Name": [""], "Description": [""], "Groupable": [""], "Filterable": [""], "Table": [""], "Expression": [""], "Is temporal": [""], "Datetime Format": [""], "Type": [""], "Business Data Type": [""], "Invalid date/timestamp format": [""], "Metrics": [""], "Show Metric": [""], "Add Metric": [""], "Edit Metric": [""], "Metric": [""], "SQL Expression": [""], "D3 Format": [""], "Extra": [""], "Warning Message": [""], "Tables": [""], "Show Table": [""], "Import a table definition": [""], "Edit Table": [""], "The list of charts associated with this table. By altering this datasource, you may change how these associated charts behave. Also note that charts need to point to a datasource, so this form will fail at saving if removing charts from a datasource. If you want to change the datasource for a chart, overwrite the chart from the 'explore view'": [""], "Timezone offset (in hours) for this datasource": [""], "Name of the table that exists in the source database": [""], "Schema, as used only in some databases like Postgres, Redshift and DB2": [""], "This fields acts a Superset view, meaning that Superset will run a query against this string as a subquery.": [""], "Predicate applied when fetching distinct value to populate the filter control component. Supports jinja template syntax. Applies only when `Enable Filter Select` is on.": [""], "Redirects to this endpoint when clicking on the table from the table list": [""], "Whether to populate the filter's dropdown in the explore view's filter section with a list of distinct values fetched from the backend on the fly": [""], "Whether the table was generated by the 'Visualize' flow in SQL Lab": [""], "A set of parameters that become available in the query using Jinja templating syntax": [""], "Duration (in seconds) of the caching timeout for this table. A timeout of 0 indicates that the cache never expires. Note this defaults to the database timeout if undefined.": [""], "Allow column names to be changed to case insensitive format, if supported (e.g. Oracle, Snowflake).": [""], "Datasets can have a main temporal column (main_dttm_col), but can also have secondary time columns. When this attribute is true, whenever the secondary columns are filtered, the same filter is applied to the main datetime column.": [""], "Associated Charts": [""], "Changed By": [""], "Database": [""], "Last Changed": [""], "Enable Filter Select": [""], "Schema": [""], "Default Endpoint": [""], "Offset": [""], "Cache Timeout": [""], "Table Name": [""], "Fetch Values Predicate": [""], "Owners": [""], "Main Datetime Column": [""], "SQL Lab View": [""], "Template parameters": [""], "Modified": [""], "The table was created. As part of this two-phase configuration process, you should now click the edit button by the new table to configure it.": [""], "Deleted %(num)d css template": ["", "Deleted %(num)d css templates"], "Dataset schema is invalid, caused by: %(error)s": [""], "Tab schema is invalid, caused by: %(error)s": [""], "Deleted %(num)d dashboard": ["", "Deleted %(num)d dashboards"], "Title or Slug": [""], "Role": [""], "Invalid state.": [""], "Table name undefined": [""], "Upload Enabled": [""], "Invalid connection string, a valid string usually follows: backend+driver://user:password@database-host/database-name": [""], "Field cannot be decoded by JSON. %(msg)s": [""], "The metadata_params in Extra field is not configured correctly. The key %(key)s is invalid.": [""], "An engine must be specified when passing individual parameters to a database.": [""], "Engine spec \"InvalidEngine\" does not support being configured via individual parameters.": [""], "File extension is not allowed.": [""], "File size exceeds the maximum allowed size.": [""], "Deleted %(num)d dataset": ["", "Deleted %(num)d datasets"], "Null or Empty": [""], "Unknown Presto Error": [""], "Samples for datasource could not be retrieved.": [""], "Changing this datasource is forbidden": [""], "An error occurred while parsing the key.": [""], "An error occurred while upserting the value.": [""], "Unable to encode value": [""], "Unable to decode value": [""], "Invalid permalink key": [""], "Error while rendering virtual dataset query: %(msg)s": [""], "Virtual dataset query cannot consist of multiple statements": [""], "Datetime column not provided as part table configuration and is required by this type of chart": [""], "Empty query?": [""], "Unknown column used in orderby: %(col)s": [""], "Time column \"%(col)s\" does not exist in dataset": [""], "error_message": [""], "Filter value list cannot be empty": [""], "Must specify a value for filters with comparison operators": [""], "Invalid filter operation type: %(op)s": [""], "Error in jinja expression in WHERE clause: %(msg)s": [""], "Error in jinja expression in HAVING clause: %(msg)s": [""], "Database does not support subqueries": [""], "Deleted %(num)d saved query": ["", "Deleted %(num)d saved queries"], "Deleted %(num)d report schedule": ["", "Deleted %(num)d report schedules"], "Value must be greater than 0": [""], "Custom width of the screenshot in pixels": [""], "Screenshot width must be between %(min)spx and %(max)spx": [""], "Value must be 0 or greater": [""], "%(dialect)s cannot be used as a data source for security reasons.": [""], "Country": [""], "Team": [""], "User info": [""], "Personal Info": [""], "Audit Info": [""], "Guest user cannot modify chart payload": [""], "You don't have the rights to alter %(resource)s": [""], "Failed to execute %(query)s": [""], "The parameter %(parameters)s in your query is undefined.": ["", "The following parameters in your query are undefined: %(parameters)s."], "finished": [""], "id": [""], "first_name": [""], "Tag name is invalid (cannot contain ':')": [""], "Tag could not be found.": [""], "Is custom tag": [""], "Is tagged": [""], "Scheduled task executor not found": [""], "Name": [""], "Slug": [""], "External": [""], "Record Count": [""], "No records found": [""], "Filter List": [""], "Search": [""], "Refresh": [""], "Import dashboards": [""], "Import Dashboard(s)": [""], "File": [""], "Choose File": [""], "Upload": [""], "Use the edit button to change this field": [""], "Test Connection": [""], "Unable to calculate such a date delta": [""], "Unable to find such a holiday: [%(holiday)s]": [""], "DB column %(col_name)s has unknown type: %(value_type)s": [""], "percentiles must be a list or tuple with two numeric values, of which the first is lower than the second value": [""], "`compare_columns` must have the same length as `source_columns`.": [""], "`compare_type` must be `difference`, `percentage` or `ratio`": [""], "Column \"%(column)s\" is not numeric or does not exists in the query results.": [""], "`rename_columns` must have the same length as `columns` + `time_shift_columns`.": [""], "Invalid cumulative operator: %(operator)s": [""], "Invalid geohash string": [""], "Invalid longitude/latitude": [""], "Invalid geodetic string": [""], "Pivot operation requires at least one index": [""], "Pivot operation must include at least one aggregate": [""], "`prophet` package not installed": [""], "Time grain missing": [""], "Unsupported time grain: %(time_grain)s": [""], "Periods must be a whole number": [""], "Confidence interval must be between 0 and 1 (exclusive)": [""], "DataFrame must include temporal column": [""], "DataFrame include at least one series": [""], "Label already exists": [""], "Resample operation requires DatetimeIndex": [""], "Resample method should be in ": [""], "Undefined window for rolling operation": [""], "Window must be > 0": [""], "Invalid rolling_type: %(type)s": [""], "Invalid options for %(rolling_type)s: %(options)s": [""], "Referenced columns not available in DataFrame.": [""], "Column referenced by aggregate is undefined: %(column)s": [""], "Operator undefined for aggregator: %(name)s": [""], "Invalid numpy function: %(operator)s": [""], "Unexpected time range: %(error)s": [""], "Is favorite": [""], "You don't have the rights to download as csv": [""], "Error: permalink state not found": [""], "You don't have the rights to alter this chart": [""], "You don't have the rights to create a chart": [""], "Explore - %(table)s": [""], "Explore": [""], "Chart [{}] has been saved": [""], "Chart [{}] has been overwritten": [""], "You don't have the rights to alter this dashboard": [""], "Chart [{}] was added to dashboard [{}]": [""], "You don't have the rights to create a dashboard": [""], "Dashboard [{}] just got created and chart [{}] was added to it": [""], "permalink state not found": [""], "A human-friendly name": [""], "Used internally to identify the plugin. Should be set to the package name from the pluginʼs package.json": [""], "A full URL pointing to the location of the built plugin (could be hosted on a CDN for example)": [""], "Custom Plugins": [""], "Custom Plugin": [""], "Add a Plugin": [""], "Edit Plugin": [""], "The dataset associated with this chart no longer exists": [""], "Could not determine datasource type": [""], "Could not find viz object": [""], "Charts": [""], "Show Chart": [""], "Add Chart": [""], "Edit Chart": [""], "These parameters are generated dynamically when clicking the save or overwrite button in the explore view. This JSON object is exposed here for reference and for power users who may want to alter specific parameters.": [""], "Duration (in seconds) of the caching timeout for this chart. Note this defaults to the datasource/table timeout if undefined.": [""], "Creator": [""], "Dashboards": [""], "Datasource": [""], "Last Modified": [""], "Parameters": [""], "Chart": [""], "Visualization Type": [""], "Show Dashboard": [""], "Add Dashboard": [""], "Edit Dashboard": [""], "This json object describes the positioning of the widgets in the dashboard. It is dynamically generated when adjusting the widgets size and positions by using drag & drop in the dashboard view": [""], "The CSS for individual dashboards can be altered here, or in the dashboard view where changes are immediately visible": [""], "To get a readable URL for your dashboard": [""], "This JSON object is generated dynamically when clicking the save or overwrite button in the dashboard view. It is exposed here for reference and for power users who may want to alter specific parameters.": [""], "Owners is a list of users who can alter the dashboard.": [""], "Roles is a list which defines access to the dashboard. Granting a role access to a dashboard will bypass dataset level checks.If no roles are defined, regular access permissions apply.": [""], "Determines whether or not this dashboard is visible in the list of all dashboards": [""], "Dashboard": [""], "Title": [""], "Roles": [""], "Published": [""], "Position JSON": [""], "CSS": [""], "JSON Metadata": [""], "Databases": [""], "Show Database": [""], "Add Database": [""], "Edit Database": [""], "Expose this DB in SQL Lab": [""], "Operate the database in asynchronous mode, meaning that the queries are executed on remote workers as opposed to on the web server itself. This assumes that you have a Celery worker setup as well as a results backend. Refer to the installation docs for more information.": [""], "Allow CREATE TABLE AS option in SQL Lab": [""], "Allow CREATE VIEW AS option in SQL Lab": [""], "Allow users to run non-SELECT statements (UPDATE, DELETE, CREATE, ...) in SQL Lab": [""], "When allowing CREATE TABLE AS option in SQL Lab, this option forces the table to be created in this schema": [""], "If Presto, all the queries in SQL Lab are going to be executed as the currently logged on user who must have permission to run them.<br/>If Hive and hive.server2.enable.doAs is enabled, will run the queries as service account, but impersonate the currently logged on user via hive.server2.proxy.user property.": [""], "Duration (in seconds) of the caching timeout for charts of this database. A timeout of 0 indicates that the cache never expires. Note this defaults to the global timeout if undefined.": [""], "If selected, please set the schemas allowed for csv upload in Extra.": [""], "Expose in SQL Lab": [""], "Allow CREATE TABLE AS": [""], "Allow CREATE VIEW AS": [""], "Allow DDL/DML": [""], "CTAS Schema": [""], "SQLAlchemy URI": [""], "Chart Cache Timeout": [""], "Secure Extra": [""], "Root certificate": [""], "Async Execution": [""], "Impersonate the logged on user": [""], "Allow Csv Upload": [""], "Backend": [""], "Extra field cannot be decoded by JSON. %(msg)s": [""], "Invalid connection string, a valid string usually follows:'DRIVER://USER:PASSWORD@DB-HOST/DATABASE-NAME'<p>Example:'****************************************************'</p>": [""], "Request missing data field.": [""], "Duplicate column name(s): %(columns)s": [""], "Logs": [""], "Show Log": [""], "Add Log": [""], "Edit Log": [""], "User": [""], "Action": [""], "dttm": [""], "JSON": [""], "Statements": [""], "Show Statement": [""], "Add Statement": [""], "Edit Statement": [""], "is_external": [""], "is_new_team": [""], "Team slug": [""], "Finished": [""], "Request roles": [""], "Created datetime": [""], "Last changed datetime": [""], "Show Team": [""], "Add Team": [""], "Edit Team": [""], "Id": [""], "Participants": [""], "Time Range": [""], "Time Column": [""], "Time Grain": [""], "Time Granularity": [""], "Time": [""], "A reference to the [Time] configuration, taking granularity into account": [""], "Aggregate": [""], "Raw records": [""], "Category name": [""], "Total value": [""], "Minimum value": [""], "Maximum value": [""], "Average value": [""], "Count": [""], "Count Unique Values": [""], "List Unique Values": [""], "Sum": [""], "Average": [""], "Median": [""], "Sample Variance": [""], "Sample Standard Deviation": [""], "Minimum": [""], "Maximum": [""], "First": [""], "Last": [""], "Sum as Fraction of Total": [""], "Sum as Fraction of Rows": [""], "Sum as Fraction of Columns": [""], "Count as Fraction of Total": [""], "Count as Fraction of Rows": [""], "Count as Fraction of Columns": [""], "Certified by %s": [""], "description": [""], "bolt": [""], "Changing this control takes effect instantly": [""], "Show info tooltip": [""], "SQL expression": [""], "Column type": [""], "Column name": [""], "Label": [""], "Metric name": [""], "unknown type icon": [""], "function type icon": [""], "string type icon": [""], "numeric type icon": [""], "boolean type icon": [""], "temporal type icon": [""], "Advanced analytics": [""], "This section contains options that allow for advanced analytical post processing of query results": [""], "Rolling window": [""], "Rolling function": [""], "None": [""], "Defines a rolling window function to apply, works along with the [Periods] text box": [""], "Periods": [""], "Defines the size of the rolling window function, relative to the time granularity selected": [""], "Min periods": [""], "The minimum number of rolling periods required to show a value. For instance if you do a cumulative sum on 7 days you may want your \"Min Period\" to be 7, so that all data points shown are the total of 7 periods. This will hide the \"ramp up\" taking place over the first 7 periods": [""], "Time comparison": [""], "Time shift": [""], "1 day ago": [""], "1 week ago": [""], "28 days ago": [""], "30 days ago": [""], "52 weeks ago": [""], "1 year ago": [""], "104 weeks ago": [""], "2 years ago": [""], "156 weeks ago": [""], "3 years ago": [""], "Overlay one or more timeseries from a relative time period. Expects relative time deltas in natural language (example:  24 hours, 7 days, 52 weeks, 365 days). Free text is supported.": [""], "Calculation type": [""], "Actual values": [""], "Difference": [""], "Percentage change": [""], "Ratio": [""], "How to display time shifts: as individual lines; as the difference between the main time series and each time shift; as the percentage change; or as the ratio between series and time shifts.": [""], "Resample": [""], "Rule": [""], "1 minutely frequency": [""], "1 hourly frequency": [""], "1 calendar day frequency": [""], "7 calendar day frequency": [""], "1 month start frequency": [""], "1 month end frequency": [""], "1 year start frequency": [""], "1 year end frequency": [""], "Pandas resample rule": [""], "Fill method": [""], "Null imputation": [""], "Zero imputation": [""], "Linear interpolation": [""], "Forward values": [""], "Backward values": [""], "Median values": [""], "Mean values": [""], "Sum values": [""], "Pandas resample method": [""], "Annotations and Layers": [""], "Annotation Layers": [""], "Left": [""], "Top": [""], "Chart Title": [""], "X Axis": [""], "X Axis Title": [""], "X AXIS TITLE BOTTOM MARGIN": [""], "Y Axis": [""], "Y Axis Title": [""], "Y Axis Title Margin": [""], "Y Axis Title Position": [""], "Query": [""], "Predictive Analytics": [""], "Enable forecast": [""], "Enable forecasting": [""], "Forecast periods": [""], "How many periods into the future do we want to predict": [""], "Confidence interval": [""], "Width of the confidence interval. Should be between 0 and 1": [""], "Yearly seasonality": [""], "default": [""], "Yes": [""], "No": [""], "Should yearly seasonality be applied. An integer value will specify Fourier order of seasonality.": [""], "Weekly seasonality": [""], "Should weekly seasonality be applied. An integer value will specify Fourier order of seasonality.": [""], "Daily seasonality": [""], "Should daily seasonality be applied. An integer value will specify Fourier order of seasonality.": [""], "Time related form attributes": [""], "Datasource & Chart Type": [""], "Chart ID": [""], "The id of the active chart": [""], "Cache Timeout (seconds)": [""], "The number of seconds before expiring the cache": [""], "URL Parameters": [""], "Extra url parameters for use in Jinja templated queries": [""], "Extra Parameters": [""], "Extra parameters that any plugins can choose to set for use in Jinja templated queries": [""], "Color Scheme": [""], "1 month ago": [""], "Custom date": [""], "Inherit range from time filter": [""], "Time Comparison": [""], "Compare results with other time periods.": [""], "Select or type a custom value...": [""], "Overlay results from a relative time period. Expects relative time deltas in natural language (example:  24 hours, 7 days, 52 weeks, 365 days). Free text is supported. Use \"Inherit range from time filters\" to shift the comparison time range by the same length as your time range and use \"Custom\" to set a custom comparison range.": [""], "shift start date": [""], "Contribution Mode": [""], "Row": [""], "Series": [""], "Calculate contribution per series or row": [""], "Y-Axis Sort By": [""], "X-Axis Sort By": [""], "Decides which column to sort the base axis by.": [""], "Y-Axis Sort Ascending": [""], "X-Axis Sort Ascending": [""], "Whether to sort ascending or descending on the base Axis.": [""], "Force categorical": [""], "Treat values as categorical.": [""], "Decides which measure to sort the base axis by.": [""], "Dimensions": [""], "Dimensions contain qualitative values such as names, dates, or geographical data. Use dimensions to categorize, segment, and reveal the details in your data. Dimensions affect the level of detail in the view.": [""], "Add dataset columns here to group the pivot table columns.": [""], "Dimension": [""], "Defines the grouping of entities. Each series is represented by a specific color in the chart.": [""], "Entity": [""], "This defines the element to be plotted on the chart": [""], "Filters": [""], "Select one or many metrics to display. You can use an aggregation function on a column or write custom SQL to create a metric.": [""], "Select a metric to display. You can use an aggregation function on a column or write custom SQL to create a metric.": [""], "Right Axis Metric": [""], "Select a metric to display on the right axis": [""], "Sort by": [""], "This metric is used to define row selection criteria (how the rows are sorted) if a series or row limit is present. If not defined, it reverts to the first metric (where appropriate).": [""], "Bubble Size": [""], "Metric used to calculate bubble size": [""], "The dataset column/metric that returns the values on your chart's x-axis.": [""], "The dataset column/metric that returns the values on your chart's y-axis.": [""], "Color Metric": [""], "A metric to use for color": [""], "The time column for the visualization. Note that you can define arbitrary expression that return a DATETIME column in the table. Also note that the filter below is applied against this column or expression": [""], "Drop a temporal column here or click": [""], "Y-axis": [""], "Dimension to use on y-axis.": [""], "X-axis": [""], "Dimension to use on x-axis.": [""], "The type of visualization to display": [""], "Fixed Color": [""], "Use this to define a static color for all circles": [""], "Linear Color Scheme": [""], "all": [""], "5 seconds": [""], "30 seconds": [""], "1 minute": [""], "5 minutes": [""], "30 minutes": [""], "1 hour": [""], "6 hour": [""], "1 day": [""], "7 days": [""], "week": [""], "week starting Sunday": [""], "week ending Saturday": [""], "month": [""], "quarter": [""], "year": [""], "The time granularity for the visualization. Note that you can type and use simple natural language as in `10 seconds`, `1 day` or `56 weeks`": [""], "Select a time grain for the visualization. The grain is the time interval represented by a single point on the chart.": [""], "This control filters the whole chart based on the selected time range. All relative times, e.g. \"Last month\", \"Last 7 days\", \"now\", etc. are evaluated on the server using the server's local time (sans timezone). All tooltips and placeholder times are expressed in UTC (sans timezone). The timestamps are then evaluated by the database using the engine's local timezone. Note one can explicitly set the timezone per the ISO 8601 format if specifying either the start and/or end time.": [""], "Row limit": [""], "Limits the number of the rows that are computed in the query that is the source of the data used for this chart.": [""], "Sort Descending": [""], "If enabled, this control sorts the results/values descending, otherwise it sorts the results ascending.": [""], "Series limit": [""], "Limits the number of series that get displayed. A joined subquery (or an extra phase where subqueries are not supported) is applied to limit the number of series that get fetched and rendered. This feature is useful when grouping by high cardinality column(s) though does increase the query complexity and cost.": [""], "Y Axis Format": [""], "Currency format": [""], "Time format": [""], "The color scheme for rendering chart": [""], "Truncate Metric": [""], "Whether to truncate metrics": [""], "Show empty columns": [""], "Sort by metric": [""], "Whether to sort results by the selected metric in descending order.": [""], "Size format": [""], "D3 format syntax: https://github.com/d3/d3-format": [""], "Only applies when \"Label Type\" is set to show values.": [""], "Only applies when \"Label Type\" is not set to a percentage.": [""], "With space": [""], "With space rounded": [""], "Rounded": [""], "With space rounded 1": [""], "With space rounded 2": [""], "With space rounded 3": [""], "Adaptive formatting": [""], "Original value": [""], "Duration in ms (66000 => 1m 6s)": [""], "Duration in ms (1.40008 => 1ms 400µs 80ns)": [""], "Duration in ms (66000 => 0:01:06)": [""], "D3 time format syntax: https://github.com/d3/d3-time-format": [""], "Oops! An error occurred!": [""], "Stack Trace:": [""], "No results were returned for this query. If you expected results to be returned, ensure any filters are configured properly and the datasource contains data for the selected time range.": [""], "No Results": [""], "ERROR": [""], "This chart uses features or modules which are no longer actively maintained. It will eventually be replaced or removed.": [""], "This chart was tested and verified, so the overall experience should be stable.": [""], "Found invalid orderby options": [""], "Unknown error": [""], "Invalid input": [""], "Unexpected error: ": [""], "(no description, click to see stack trace)": [""], "Network error": [""], "Request timed out": [""], "Issue 1000 - The dataset is too large to query.": [""], "Issue 1001 - The database is under an unusual load.": [""], "An error occurred": [""], "Sorry, an unknown error occurred.": [""], "Sorry, there was an error saving this %s: %s": [""], "You do not have permission to edit this %s": [""], "No data": [""], "is expected to be an integer": [""], "is expected to be a number": [""], "is expected to be a Mapbox URL": [""], "Value cannot exceed %s": [""], "cannot be empty": [""], "Filters for comparison must have a value": [""], "Domain": [""], "hour": [""], "day": [""], "The time unit used for the grouping of blocks": [""], "Subdomain": [""], "min": [""], "The time unit for each block. Should be a smaller unit than domain_granularity. Should be larger or equal to Time Grain": [""], "Chart Options": [""], "Cell Size": [""], "The size of the square cell, in pixels": [""], "Cell Padding": [""], "The distance between cells, in pixels": [""], "Cell Radius": [""], "The pixel radius": [""], "Color Steps": [""], "The number color \"steps\"": [""], "Time Format": [""], "Legend": [""], "Whether to display the legend (toggles)": [""], "Show Values": [""], "Whether to display the numerical values within the cells": [""], "Show Metric Names": [""], "Whether to display the metric name as a title": [""], "Number Format": [""], "Correlation": [""], "Visualizes how a metric has changed over a time using a color scale and a calendar view. Gray values are used to indicate missing values and the linear color scheme is used to encode the magnitude of each day's value.": [""], "Business": [""], "Comparison": [""], "Intensity": [""], "Pattern": [""], "Report": [""], "Trend": [""], "less than {min} {name}": [""], "between {down} and {up} {name}": [""], "more than {max} {name}": [""], "Number format": [""], "Choose a number format": [""], "Source": [""], "Choose a source": [""], "Target": [""], "Choose a target": [""], "Flow": [""], "Showcases the flow or link between categories using thickness of chords. The value and corresponding thickness can be different for each side.": [""], "Relationships between community channels": [""], "Chord Diagram": [""], "Circular": [""], "Legacy": [""], "Proportional": [""], "Relational": [""], "Which country to plot the map for?": [""], "ISO 3166-2 Codes": [""], "Column containing ISO 3166-2 codes of region/province/department in your table.": [""], "Metric to display bottom title": [""], "Map": [""], "Visualizes how a single metric varies across a country's principal subdivisions (states, provinces, etc) on a choropleth map. Each subdivision's value is elevated when you hover over the corresponding geographic boundary.": [""], "2D": [""], "Geo": [""], "Range": [""], "Stacked": [""], "Sorry, there appears to be no data": [""], "Event definition": [""], "Event Names": [""], "Columns to display": [""], "Order by entity id": [""], "Important! Select this if the table is not already sorted by entity id, else there is no guarantee that all events for each entity are returned.": [""], "Minimum leaf node event count": [""], "Leaf nodes that represent fewer than this number of events will be initially hidden in the visualization": [""], "Additional metadata": [""], "Metadata": [""], "Select any columns for metadata inspection": [""], "Entity ID": [""], "e.g., a \"user id\" column": [""], "Max Events": [""], "The maximum number of events to return, equivalent to the number of rows": [""], "Compares the lengths of time different activities take in a shared timeline view.": [""], "Event Flow": [""], "Progressive": [""], "Axis ascending": [""], "Axis descending": [""], "Metric ascending": [""], "Metric descending": [""], "Heatmap Options": [""], "XScale Interval": [""], "Number of steps to take between ticks when displaying the X scale": [""], "YScale Interval": [""], "Number of steps to take between ticks when displaying the Y scale": [""], "Rendering": [""], "pixelated (Sharp)": [""], "auto (Smooth)": [""], "image-rendering CSS attribute of the canvas object that defines how the browser scales up the image": [""], "Normalize Across": [""], "heatmap": [""], "x": [""], "y": [""], "Color will be shaded based the normalized (0% to 100%) value of a given cell against the other cells in the selected range: ": [""], "x: values are normalized within each column": [""], "y: values are normalized within each row": [""], "heatmap: values are normalized across the entire heatmap": [""], "Left Margin": [""], "auto": [""], "Left margin, in pixels, allowing for more room for axis labels": [""], "Bottom Margin": [""], "Bottom margin, in pixels, allowing for more room for axis labels": [""], "Value bounds": [""], "Hard value bounds applied for color coding. Is only relevant and applied when the normalization is applied against the whole heatmap.": [""], "Sort X Axis": [""], "Sort Y Axis": [""], "Show percentage": [""], "Whether to include the percentage in the tooltip": [""], "Normalized": [""], "Whether to apply a normal distribution based on rank on the color scale": [""], "Value Format": [""], "Visualize a related metric across pairs of groups. Heatmaps excel at showcasing the correlation or strength between two groups. Color is used to emphasize the strength of the link between each pair of groups.": [""], "Sizes of vehicles": [""], "Employment and education": [""], "Heatmap (legacy)": [""], "Density": [""], "Predictive": [""], "Single Metric": [""], "Deprecated": [""], "to": [""], "count": [""], "cumulative": [""], "percentile (exclusive)": [""], "Select the numeric columns to draw the histogram": [""], "No of Bins": [""], "Select the number of bins for the histogram": [""], "X Axis Label": [""], "Y Axis Label": [""], "Whether to normalize the histogram": [""], "Cumulative": [""], "Whether to make the histogram cumulative": [""], "Distribution": [""], "Take your data points, and group them into \"bins\" to see where the densest areas of information lie": [""], "Population age data": [""], "Histogram (legacy)": [""], "Contribution": [""], "Compute the contribution to the total": [""], "Series Height": [""], "Pixel height of each series": [""], "Value Domain": [""], "series": [""], "overall": [""], "change": [""], "series: Treat each series independently; overall: All series use the same scale; change: Show changes compared to the first data point in each series": [""], "Compares how a metric changes over time between different groups. Each group is mapped to a row and change over time is visualized bar lengths and color.": [""], "Horizon Chart": [""], "Dark Cyan": [""], "Purple": [""], "Gold": [""], "Dim Gray": [""], "Crimson": [""], "Forest Green": [""], "Longitude": [""], "Column containing longitude data": [""], "Latitude": [""], "Column containing latitude data": [""], "Clustering Radius": [""], "The radius (in pixels) the algorithm uses to define a cluster. Choose 0 to turn off clustering, but beware that a large number of points (>1000) will cause lag.": [""], "Points": [""], "Point Radius": [""], "The radius of individual points (ones that are not in a cluster). Either a numerical column or `Auto`, which scales the point based on the largest cluster": [""], "Auto": [""], "Point Radius Unit": [""], "Pixels": [""], "Miles": [""], "Kilometers": [""], "The unit of measure for the specified point radius": [""], "Labelling": [""], "label": [""], "`count` is COUNT(*) if a group by is used. Numerical columns will be aggregated with the aggregator. Non-numerical columns will be used to label points. Leave empty to get a count of points in each cluster.": [""], "Cluster label aggregator": [""], "sum": [""], "mean": [""], "max": [""], "std": [""], "var": [""], "Aggregate function applied to the list of points in each cluster to produce the cluster label.": [""], "Visual Tweaks": [""], "Live render": [""], "Points and clusters will update as the viewport is being changed": [""], "Map Style": [""], "Streets": [""], "Dark": [""], "Light": [""], "Satellite Streets": [""], "Satellite": [""], "Outdoors": [""], "Base layer map style. See Mapbox documentation: %s": [""], "Opacity": [""], "Opacity of all clusters, points, and labels. Between 0 and 1.": [""], "RGB Color": [""], "The color for points and clusters in RGB": [""], "Viewport": [""], "Default longitude": [""], "Longitude of default viewport": [""], "Default latitude": [""], "Latitude of default viewport": [""], "Zoom": [""], "Zoom level of the map": [""], "One or many controls to group by. If grouping, latitude and longitude columns must be present.": [""], "Light mode": [""], "Dark mode": [""], "MapBox": [""], "Scatter": [""], "Transformable": [""], "Significance Level": [""], "Threshold alpha level for determining significance": [""], "p-value precision": [""], "Number of decimal places with which to display p-values": [""], "Lift percent precision": [""], "Number of decimal places with which to display lift values": [""], "Table that visualizes paired t-tests, which are used to understand statistical differences between groups.": [""], "Paired t-test Table": [""], "Statistical": [""], "Tabular": [""], "Options": [""], "Data Table": [""], "Whether to display the interactive data table": [""], "Include Series": [""], "Include series name as an axis": [""], "Ranking": [""], "Plots the individual metrics for each row in the data vertically and links them together as a line. This chart is useful for comparing multiple metrics across all of the samples or rows in the data.": [""], "Directional": [""], "Time Series Options": [""], "Not Time Series": [""], "Ignore time": [""], "Time Series": [""], "Standard time series": [""], "Aggregate Mean": [""], "Mean of values over specified period": [""], "Aggregate Sum": [""], "Sum of values over specified period": [""], "Metric change in value from `since` to `until`": [""], "Percent Change": [""], "Metric percent change in value from `since` to `until`": [""], "Factor": [""], "Metric factor change from `since` to `until`": [""], "Advanced Analytics": [""], "Use the Advanced Analytics options below": [""], "Settings for time series": [""], "Date Time Format": [""], "Partition Limit": [""], "The maximum number of subdivisions of each group; lower values are pruned first": [""], "Partition Threshold": [""], "Partitions whose height to parent height proportions are below this value are pruned": [""], "Log Scale": [""], "Use a log scale": [""], "Equal Date Sizes": [""], "Check to force date partitions to have the same height": [""], "Rich Tooltip": [""], "The rich tooltip shows a list of all series for that point in time": [""], "Rolling Window": [""], "Rolling Function": [""], "cumsum": [""], "Min Periods": [""], "Time Shift": [""], "1 week": [""], "28 days": [""], "30 days": [""], "52 weeks": [""], "1 year": [""], "104 weeks": [""], "2 years": [""], "156 weeks": [""], "3 years": [""], "Overlay one or more timeseries from a relative time period. Expects relative time deltas in natural language (example: 24 hours, 7 days, 52 weeks, 365 days). Free text is supported.": [""], "Actual Values": [""], "1T": [""], "1H": [""], "1D": [""], "7D": [""], "1M": [""], "1AS": [""], "Method": [""], "asfreq": [""], "bfill": [""], "ffill": [""], "median": [""], "Part of a Whole": [""], "Compare the same summarized metric across multiple groups.": [""], "Partition Chart": [""], "Categorical": [""], "Use Area Proportions": [""], "Check if the Rose Chart should use segment area instead of segment radius for proportioning": [""], "A polar coordinate chart where the circle is broken into wedges of equal angle, and the value represented by any wedge is illustrated by its area, rather than its radius or sweep angle.": [""], "Nightingale Rose Chart": [""], "Advanced-Analytics": [""], "Multi-Layers": [""], "Source / Target": [""], "Choose a source and a target": [""], "Limiting rows may result in incomplete data and misleading charts. Consider filtering or grouping source/target names instead.": [""], "Visualizes the flow of different group's values through different stages of a system. New stages in the pipeline are visualized as nodes or layers. The thickness of the bars or edges represent the metric being visualized.": [""], "Demographics": [""], "Survey Responses": [""], "Sankey Diagram (legacy)": [""], "Percentages": [""], "Sankey Diagram with Loops": [""], "Country Field Type": [""], "Full name": [""], "code International Olympic Committee (cioc)": [""], "code ISO 3166-1 alpha-2 (cca2)": [""], "code ISO 3166-1 alpha-3 (cca3)": [""], "The country code standard that Superset should expect to find in the [country] column": [""], "Show Bubbles": [""], "Whether to display bubbles on top of countries": [""], "Max Bubble Size": [""], "Color by": [""], "Choose whether a country should be shaded by the metric, or assigned a color based on a categorical color palette": [""], "Country Column": [""], "3 letter code of the country": [""], "Metric that defines the size of the bubble": [""], "Bubble Color": [""], "Country Color Scheme": [""], "A map of the world, that can indicate values in different countries.": [""], "Multi-Dimensions": [""], "Multi-Variables": [""], "Featured": [""], "deck.gl charts": [""], "Pick a set of deck.gl charts to layer on top of one another": [""], "Select charts": [""], "Error while fetching charts": [""], "Compose multiple layers together to form complex visuals.": [""], "deck.gl Multiple Layers": [""], "deckGL": [""], "Start (Longitude, Latitude): ": [""], "End (Longitude, Latitude): ": [""], "Start Longitude & Latitude": [""], "Point to your spatial columns": [""], "End Longitude & Latitude": [""], "Arc": [""], "Target Color": [""], "Color of the target location": [""], "Categorical Color": [""], "Pick a dimension from which categorical colors are defined": [""], "Stroke Width": [""], "Advanced": [""], "Plot the distance (like flight paths) between origin and destination.": [""], "deck.gl Arc": [""], "3D": [""], "Web": [""], "Centroid (Longitude and Latitude): ": [""], "Threshold: ": [""], "The size of each cell in meters": [""], "Aggregation": [""], "The function to use when aggregating points into groups": [""], "Contours": [""], "Define contour layers. Isolines represent a collection of line segments that serparate the area above and below a given threshold. Isobands represent a collection of polygons that fill the are containing values in a given threshold range.": [""], "Weight": [""], "Metric used as a weight for the grid's coloring": [""], "Uses Gaussian Kernel Density Estimation to visualize spatial distribution of data": [""], "deck.gl Contour": [""], "Spatial": [""], "GeoJson Settings": [""], "Line width unit": [""], "meters": [""], "pixels": [""], "Point Radius Scale": [""], "The GeoJsonLayer takes in GeoJSON formatted data and renders it as interactive polygons, lines and points (circles, icons and/or texts).": [""], "deck.gl Geojson": [""], "Longitude and Latitude": [""], "Height": [""], "Metric used to control height": [""], "Visualize geospatial data like 3D buildings, landscapes, or objects in grid view.": [""], "deck.gl Grid": [""], "Intesity": [""], "Intensity is the value multiplied by the weight to obtain the final weight": [""], "Intensity Radius": [""], "Intensity Radius is the radius at which the weight is distributed": [""], "deck.gl Heatmap": [""], "Dynamic Aggregation Function": [""], "variance": [""], "deviation": [""], "p1": [""], "p5": [""], "p95": [""], "p99": [""], "Overlays a hexagonal grid on a map, and aggregates data within the boundary of each cell.": [""], "deck.gl 3D Hexagon": [""], "Polyline": [""], "Visualizes connected points, which form a path, on a map.": [""], "deck.gl Path": [""], "name": [""], "Polygon Column": [""], "Polygon Encoding": [""], "Elevation": [""], "Polygon Settings": [""], "Opacity, expects values between 0 and 100": [""], "Number of buckets to group data": [""], "How many buckets should the data be grouped in.": [""], "Bucket break points": [""], "List of n+1 values for bucketing metric into n buckets.": [""], "Emit Filter Events": [""], "Whether to apply filter when items are clicked": [""], "Multiple filtering": [""], "Allow sending multiple polygons as a filter event": [""], "Visualizes geographic areas from your data as polygons on a Mapbox rendered map. Polygons can be colored using a metric.": [""], "deck.gl Polygon": [""], "Category": [""], "Point Size": [""], "Point Unit": [""], "Square meters": [""], "Square kilometers": [""], "Square miles": [""], "Radius in meters": [""], "Radius in kilometers": [""], "Radius in miles": [""], "Minimum Radius": [""], "Minimum radius size of the circle, in pixels. As the zoom level changes, this insures that the circle respects this minimum radius.": [""], "Maximum Radius": [""], "Maximum radius size of the circle, in pixels. As the zoom level changes, this insures that the circle respects this maximum radius.": [""], "Point Color": [""], "A map that takes rendering circles with a variable radius at latitude/longitude coordinates": [""], "deck.gl Scatterplot": [""], "Grid": [""], "Aggregates data within the boundary of grid cells and maps the aggregated values to a dynamic color scale": [""], "deck.gl Screen Grid": [""], "For more information about objects are in context in the scope of this function, refer to the": [""], " source code of Superset's sandboxed parser": [""], "This functionality is disabled in your environment for security reasons.": [""], "Ignore null locations": [""], "Whether to ignore locations that are null": [""], "Auto Zoom": [""], "When checked, the map will zoom to your data after each query": [""], "Select a dimension": [""], "Extra data for JS": [""], "List of extra columns made available in JavaScript functions": [""], "JavaScript data interceptor": [""], "Define a javascript function that receives the data array used in the visualization and is expected to return a modified version of that array. This can be used to alter properties of the data, filter, or enrich the array.": [""], "JavaScript tooltip generator": [""], "Define a function that receives the input and outputs the content for a tooltip": [""], "JavaScript onClick href": [""], "Define a function that returns a URL to navigate to when user clicks": [""], "Legend Format": [""], "Choose the format for legend values": [""], "Legend Position": [""], "Choose the position of the legend": [""], "Top left": [""], "Top right": [""], "Bottom left": [""], "Bottom right": [""], "Lines column": [""], "The database columns that contains lines information": [""], "Line width": [""], "The width of the lines": [""], "Fill Color": [""], " Set the opacity to 0 if you do not want to override the color specified in the GeoJSON": [""], "Stroke Color": [""], "Filled": [""], "Whether to fill the objects": [""], "Stroked": [""], "Whether to display the stroke": [""], "Extruded": [""], "Whether to make the grid 3D": [""], "Grid Size": [""], "Defines the grid size in pixels": [""], "Parameters related to the view and perspective on the map": [""], "Longitude & Latitude": [""], "Fixed point radius": [""], "Multiplier": [""], "Factor to multiply the metric by": [""], "Lines encoding": [""], "The encoding format of the lines": [""], "geohash (square)": [""], "Reverse Lat & Long": [""], "GeoJson Column": [""], "Select the geojson column": [""], "Right Axis Format": [""], "Show Markers": [""], "Show data points as circle markers on the lines": [""], "Y bounds": [""], "Whether to display the min and max values of the Y-axis": [""], "Y 2 bounds": [""], "Line Style": [""], "linear": [""], "basis": [""], "cardinal": [""], "monotone": [""], "step-before": [""], "step-after": [""], "Line interpolation as defined by d3.js": [""], "Show Range Filter": [""], "Whether to display the time range interactive selector": [""], "Extra Controls": [""], "Whether to show extra controls or not. Extra controls include things like making mulitBar charts stacked or side by side.": [""], "X Tick Layout": [""], "flat": [""], "staggered": [""], "The way the ticks are laid out on the X-axis": [""], "X Axis Format": [""], "Y Log Scale": [""], "Use a log scale for the Y-axis": [""], "Y Axis Bounds": [""], "Bounds for the Y-axis. When left empty, the bounds are dynamically defined based on the min/max of the data. Note that this feature will only expand the axis range. It won't narrow the data's extent.": [""], "Y Axis 2 Bounds": [""], "X bounds": [""], "Whether to display the min and max values of the X-axis": [""], "Bar Values": [""], "Show the value on top of the bar": [""], "Stacked Bars": [""], "Reduce X ticks": [""], "Reduces the number of X-axis ticks to be rendered. If true, the x-axis will not overflow and labels may be missing. If false, a minimum width will be applied to columns and the width may overflow into an horizontal scroll.": [""], "You cannot use 45° tick layout along with the time range filter": [""], "Stacked Style": [""], "stack": [""], "stream": [""], "expand": [""], "Evolution": [""], "A time series chart that visualizes how a related metric from multiple groups vary over time. Each group is visualized using a different color.": [""], "Stretched style": [""], "Stacked style": [""], "Video game consoles": [""], "Vehicle Types": [""], "Time-series Area Chart (legacy)": [""], "Continuous": [""], "Line": [""], "nvd3": [""], "Series Limit Sort By": [""], "Metric used to order the limit if a series limit is present. If undefined reverts to the first metric (where appropriate).": [""], "Series Limit Sort Descending": [""], "Whether to sort descending or ascending if a series limit is present": [""], "Visualize how a metric changes over time using bars. Add a group by column to visualize group level metrics and how they change over time.": [""], "Time-series Bar Chart (legacy)": [""], "Bar": [""], "Box Plot": [""], "X Log Scale": [""], "Use a log scale for the X-axis": [""], "Visualizes a metric across three dimensions of data in a single chart (X axis, Y axis, and bubble size). Bubbles from the same group can be showcased using bubble color.": [""], "Bubble Chart (legacy)": [""], "Ranges": [""], "Ranges to highlight with shading": [""], "Range labels": [""], "Labels for the ranges": [""], "Markers": [""], "List of values to mark with triangles": [""], "Marker labels": [""], "Labels for the markers": [""], "Marker lines": [""], "List of values to mark with lines": [""], "Marker line labels": [""], "Labels for the marker lines": [""], "KPI": [""], "Showcases the progress of a single metric against a given target. The higher the fill, the closer the metric is to the target.": [""], "Visualizes many different time-series objects in a single chart. This chart is being deprecated and we recommend using the Time-series Chart instead.": [""], "Time-series Percent Change": [""], "Sort Bars": [""], "Sort bars by x labels.": [""], "Breakdowns": [""], "Defines how each series is broken down": [""], "Compares metrics from different categories using bars. Bar lengths are used to indicate the magnitude of each value and color is used to differentiate groups.": [""], "Bar Chart (legacy)": [""], "Additive": [""], "Propagate": [""], "Send range filter events to other charts": [""], "Classic chart that visualizes how metrics change over time.": [""], "Battery level over time": [""], "Time-series Line Chart (legacy)": [""], "Label Type": [""], "Category Name": [""], "Value": [""], "Percentage": [""], "Category and Value": [""], "Category and Percentage": [""], "Category, Value and Percentage": [""], "What should be shown on the label?": [""], "Donut": [""], "Do you want a donut or a pie?": [""], "Show Labels": [""], "Whether to display the labels. Note that the label only displays when the 5% threshold.": [""], "Put labels outside": [""], "Put the labels outside the pie?": [""], "Pie Chart (legacy)": [""], "Frequency": [""], "Year (freq=AS)": [""], "52 weeks starting Monday (freq=52W-MON)": [""], "1 week starting Sunday (freq=W-SUN)": [""], "1 week starting Monday (freq=W-MON)": [""], "Day (freq=D)": [""], "4 weeks (freq=4W-MON)": [""], "The periodicity over which to pivot time. Users can provide\n            \"Pandas\" offset alias.\n            Click on the info bubble for more details on accepted \"freq\" expressions.": [""], "Time-series Period Pivot": [""], "Formula": [""], "Event": [""], "Interval": [""], "Stack": [""], "Stream": [""], "Expand": [""], "Show legend": [""], "Whether to display a legend for the chart": [""], "Margin": [""], "Additional padding for legend.": [""], "Scroll": [""], "Plain": [""], "Legend type": [""], "Orientation": [""], "Bottom": [""], "Right": [""], "Legend Orientation": [""], "Show Value": [""], "Show series values on the chart": [""], "Stack series on top of each other": [""], "Only Total": [""], "Only show the total value on the stacked chart, and not show on the selected category": [""], "Percentage threshold": [""], "Minimum threshold in percentage points for showing labels.": [""], "Rich tooltip": [""], "Shows a list of all series available at that point in time": [""], "Tooltip time format": [""], "Tooltip sort by metric": [""], "Whether to sort tooltip by the selected metric in descending order.": [""], "Show total": [""], "Whether to display the total value in the tooltip": [""], "Whether to display the percentage value in the tooltip": [""], "Tooltip": [""], "Sort Series By": [""], "Based on what should series be ordered on the chart and legend": [""], "Sort Series Ascending": [""], "Sort series in ascending order": [""], "Rotate x axis label": [""], "Input field supports custom rotation. e.g. 30 for 30°": [""], "Series Order": [""], "Truncate X Axis": [""], "Truncate X Axis. Can be overridden by specifying a min or max bound. Only applicable for numercal X axis.": [""], "X Axis Bounds": [""], "Bounds for numerical X axis. Not applicable for temporal or categorical axes. When left empty, the bounds are dynamically defined based on the min/max of the data. Note that this feature will only expand the axis range. It won't narrow the data's extent.": [""], "Minor ticks": [""], "Show minor ticks on axes.": [""], "Make the x-axis categorical": [""], "Last available value seen on %s": [""], "Not up to date": [""], "No data after filtering or data is NULL for the latest time record": [""], "Try applying different filters or ensuring your datasource has data": [""], "Big Number Font Size": [""], "Tiny": [""], "Small": [""], "Normal": [""], "Large": [""], "Huge": [""], "Subheader Font Size": [""], "Default": [""], "Data for %s": [""], "Value difference between the time periods": [""], "Percentage difference between the time periods": [""], "Percent Difference format": [""], "Comparison font size": [""], "Add color for positive/negative change": [""], "color scheme for comparison": [""], "Green for increase, red for decrease": [""], "Red for increase, green for decrease": [""], "Adds color to the chart symbols based on the positive or negative change from the comparison value.": [""], "Big Number with Time Period Comparison": [""], "Display settings": [""], "Subheader": [""], "Description text that shows up below your Big Number": [""], "Date format": [""], "Force date format": [""], "Use date formatting even when metric value is not a timestamp": [""], "Export as time": [""], "Export a numeric value as number of days": [""], "Showcases a single metric front-and-center. Big number is best used to call attention to a KPI or the one thing you want your audience to focus on.": [""], "A Big Number": [""], "With a subheader": [""], "Big Number": [""], "Comparison Period Lag": [""], "Based on granularity, number of time periods to compare against": [""], "Comparison suffix": [""], "Suffix to apply after the percentage display": [""], "Show Timestamp": [""], "Whether to display the timestamp": [""], "Show Trend Line": [""], "Whether to display the trend line": [""], "Start y-axis at 0": [""], "Start y-axis at zero. Uncheck to start y-axis at minimum value in the data.": [""], "Fix to selected Time Range": [""], "Fix the trend line to the full time range specified in case filtered results do not include the start or end dates": [""], "TEMPORAL X-AXIS": [""], "Showcases a single number accompanied by a simple line chart, to call attention to an important metric along with its change over time or other dimension.": [""], "Big Number with Trendline": [""], "N/A": [""], "Whisker/outlier options": [""], "Determines how whiskers and outliers are calculated.": [""], "Tukey": [""], "Min/max (no outliers)": [""], "2/98 percentiles": [""], "9/91 percentiles": [""], "Categories to group by on the x-axis.": [""], "Distribute across": [""], "Columns to calculate distribution across.": [""], "Also known as a box and whisker plot, this visualization compares the distributions of a related metric across multiple groups. The box in the middle emphasizes the mean, median, and inner 2 quartiles. The whiskers around each box visualize the min, max, range, and outer 2 quartiles.": [""], "ECharts": [""], "Min Bubble Size": [""], "Bubble size number format": [""], "Bubble Opacity": [""], "Opacity of bubbles, 0 means completely transparent, 1 means opaque": [""], "X AXIS TITLE MARGIN": [""], "Logarithmic x-axis": [""], "Rotate y axis label": [""], "Y AXIS TITLE MARGIN": [""], "Logarithmic y-axis": [""], "Truncate Y Axis": [""], "Truncate Y Axis. Can be overridden by specifying a min or max bound.": [""], "Values align": [""], "Whether to sort descending or ascending": [""], "Show values total": [""], "Show total value for the chart without hovering": [""], "Show values separately": [""], "Show values for the chart without hovering": [""], "Stack series": [""], "Show zoom controls": [""], "Data Zoom Y": [""], "Enable data zooming controls (Y)": [""], "Data Zoom X": [""], "Enable data zooming controls (X)": [""], "Echarts Bar Chart": [""], "Popular": [""], "DODOIS_friendly": [""], "Conditional Formatting": [""], "Apply conditional color formatting to metric": [""], "Conditional formatting message": [""], "Show conditional color formatting message": [""], "Chart description": [""], "Tooltip text that shows up below this chart on dashboard": [""], "Conditional message Font Size": [""], "Alignment": [""], "Value to show": [""], "Conditional formatting": [""], "Comparison period conditional Formatting": [""], "Apply comporation period conditional color formatting": [""], "Use a log scale for the X-axis.": [""], "X Axis name": [""], "X axis name": [""], "Name location": [""], "Name gap (in pixels)": [""], "Name gap from chart grid": [""], "Use a log scale for the Y-axis.": [""], "Y Axis name": [""], "Y axis name": [""], "Dimension Options": [""], "Show dimension": [""], "Whether to display the dimension.": [""], "Grid margin top (in pixels)": [""], "Margin top for chart grid": [""], "Scroll dimension": [""], "Whether to scroll dimensions.": [""], "Label Options": [""], "Whether to display the labels.": [""], "Label location": [""], "Label font (in pixels)": [""], "Label color": [""], "Size": [""], "% calculation": [""], "Display percents in the label and tooltip as the percent of the total value, from the first step of the funnel, or from the previous step in the funnel.": [""], "Calculate from first step": [""], "Calculate from previous step": [""], "Percent of total": [""], "Labels": [""], "Label Contents": [""], "Value and Percentage": [""], "What should be shown as the label": [""], "Tooltip Contents": [""], "What should be shown as the tooltip label": [""], "Show Tooltip Labels": [""], "Whether to display the tooltip labels.": [""], "Showcases how a metric changes as the funnel progresses. This classic chart is useful for visualizing drop-off between stages in a pipeline or lifecycle.": [""], "Funnel Chart": [""], "Sequential": [""], "Columns to group by": [""], "General": [""], "Min": [""], "Minimum value on the gauge axis": [""], "Max": [""], "Maximum value on the gauge axis": [""], "Start angle": [""], "Angle at which to start progress axis": [""], "End angle": [""], "Angle at which to end progress axis": [""], "Font size": [""], "Font size for axis labels, detail value and other text elements": [""], "Value format": [""], "Additional text to add before or after the value, e.g. unit": [""], "Show pointer": [""], "Whether to show the pointer": [""], "Animation": [""], "Whether to animate the progress and the value or just display them": [""], "Axis": [""], "Show axis line ticks": [""], "Whether to show minor ticks on the axis": [""], "Show split lines": [""], "Whether to show the split lines on the axis": [""], "Split number": [""], "Number of split segments on the axis": [""], "Progress": [""], "Show progress": [""], "Whether to show the progress of gauge chart": [""], "Overlap": [""], "Whether the progress bar overlaps when there are multiple groups of data": [""], "Round cap": [""], "Style the ends of the progress bar with a round cap": [""], "Intervals": [""], "Interval bounds": [""], "Comma-separated interval bounds, e.g. 2,4,5 for intervals 0-2, 2-4 and 4-5. Last number should match the value provided for MAX.": [""], "Interval colors": [""], "Comma-separated color picks for the intervals, e.g. 1,2,4. Integers denote colors from the chosen color scheme and are 1-indexed. Length must be matching that of interval bounds.": [""], "Uses a gauge to showcase progress of a metric towards a target. The position of the dial represents the progress and the terminal value in the gauge represents the target value.": [""], "Gauge Chart": [""], "Name of the source nodes": [""], "Name of the target nodes": [""], "Source category": [""], "The category of source nodes used to assign colors. If a node is associated with more than one category, only the first will be used.": [""], "Target category": [""], "Category of target nodes": [""], "Chart options": [""], "Layout": [""], "Graph layout": [""], "Force": [""], "Layout type of graph": [""], "Edge symbols": [""], "Symbol of two ends of edge line": [""], "None -> None": [""], "None -> Arrow": [""], "Circle -> Arrow": [""], "Circle -> Circle": [""], "Enable node dragging": [""], "Whether to enable node dragging in force layout mode.": [""], "Enable graph roaming": [""], "Disabled": [""], "Scale only": [""], "Move only": [""], "Scale and Move": [""], "Whether to enable changing graph position and scaling.": [""], "Node select mode": [""], "Single": [""], "Multiple": [""], "Allow node selections": [""], "Label threshold": [""], "Minimum value for label to be displayed on graph.": [""], "Node size": [""], "Median node size, the largest node will be 4 times larger than the smallest": [""], "Edge width": [""], "Median edge width, the thickest edge will be 4 times thicker than the thinnest.": [""], "Edge length": [""], "Edge length between nodes": [""], "Gravity": [""], "Strength to pull the graph toward center": [""], "Repulsion": [""], "Repulsion strength between nodes": [""], "Friction": [""], "Friction between nodes": [""], "Displays connections between entities in a graph structure. Useful for mapping relationships and showing which nodes are important in a network. Graph charts can be configured to be force-directed or circulate. If your data has a geospatial component, try the deck.gl Arc chart.": [""], "Graph Chart": [""], "Structural": [""], "Legend Type": [""], "Piecewise": [""], "Hard value bounds applied for color coding.": [""], "Y-Axis": [""], "Numeric column used to calculate the histogram.": [""], "Bins": [""], "The number of bins for the histogram": [""], "Normalize": [""], "\n                The normalize option transforms the histogram values into proportions or\n                probabilities by dividing each bin's count by the total count of data points.\n                This normalization process ensures that the resulting values sum up to 1,\n                enabling a relative comparison of the data's distribution and providing a\n                clearer understanding of the proportion of data points within each bin.": [""], "\n                The cumulative option allows you to see how your data accumulates over different\n                values. When enabled, the histogram bars represent the running total of frequencies\n                up to each bin. This helps you understand how likely it is to encounter values\n                below a certain point. Keep in mind that enabling cumulative doesn't change your\n                original data, it just changes the way the histogram is displayed.": [""], "The histogram chart displays the distribution of a dataset by\n          representing the frequency or count of values within different ranges or bins.\n          It helps visualize patterns, clusters, and outliers in the data and provides\n          insights into its shape, central tendency, and spread.": [""], "Show values": [""], "Hide values": [""], "Series type": [""], "Smooth Line": [""], "Step - start": [""], "Step - middle": [""], "Step - end": [""], "Series chart type (line, bar etc)": [""], "Area chart": [""], "Draw area under curves. Only applicable for line types.": [""], "Opacity of area chart.": [""], "Marker": [""], "Draw a marker on data points. Only applicable for line types.": [""], "Marker size": [""], "Size of marker. Also applies to forecast observations.": [""], "Primary": [""], "Secondary": [""], "Primary or secondary y-axis": [""], "Shared query fields": [""], "Query A": [""], "Advanced analytics Query A": [""], "Query B": [""], "Advanced analytics Query B": [""], "Data Zoom": [""], "Enable data zooming controls": [""], "Minor Split Line": [""], "Draw split lines for minor y-axis ticks": [""], "Primary y-axis Bounds": [""], "Bounds for the primary Y-axis. When left empty, the bounds are dynamically defined based on the min/max of the data. Note that this feature will only expand the axis range. It won't narrow the data's extent.": [""], "Primary y-axis format": [""], "Logarithmic scale on primary y-axis": [""], "Secondary y-axis Bounds": [""], "Bounds for the secondary Y-axis. Only works when Independent Y-axis\n                bounds are enabled. When left empty, the bounds are dynamically defined\n                based on the min/max of the data. Note that this feature will only expand\n                the axis range. It won't narrow the data's extent.": [""], "Secondary y-axis format": [""], "Secondary currency format": [""], "Secondary y-axis title": [""], "Logarithmic scale on secondary y-axis": [""], "Customize Metrics": [""], "Visualize two different series using the same x-axis. Note that both series can be visualized with a different chart type (e.g. 1 using bars and 1 using a line).": [""], "Mixed Chart": [""], "Rose Type": [""], "Area": [""], "Radius": [""], "Whether to show as Nightingale chart.": [""], "Template": [""], "Label Template": [""], "Format data labels. Use variables: {name}, {value}, {percent}. \\n represents a new line. ECharts compatibility:\n{a} (series), {b} (name), {c} (value), {d} (percentage)": [""], "Put the labels outside of the pie?": [""], "Label Line": [""], "Draw line from Pie to label when labels outside?": [""], "Show Total": [""], "Whether to display the aggregate count": [""], "Pie shape": [""], "Outer Radius": [""], "Outer edge of Pie chart": [""], "Inner Radius": [""], "Inner radius of donut hole": [""], "The classic. Great for showing how much of a company each investor gets, what demographics follow your blog, or what portion of the budget goes to the military industrial complex.\n\n        Pie charts can be difficult to interpret precisely. If clarity of relative proportion is important, consider using a bar or other chart type instead.": [""], "Pie Chart": [""], "Nightingale": [""], "Total: %s": [""], "The maximum value of metrics. It is an optional configuration": [""], "Label position": [""], "Radar": [""], "Further customize how to display each metric": [""], "Circle radar shape": [""], "Radar render type, whether to display 'circle' shape.": [""], "Visualize a parallel set of metrics across multiple groups. Each group is visualized using its own line of points and each metric is represented as an edge in the chart.": [""], "Radar Chart": [""], "The column to be used as the source of the edge.": [""], "The column to be used as the target of the edge.": [""], "The Sankey chart visually tracks the movement and transformation of values across\n          system stages. Nodes represent stages, connected by links depicting value flow. Node\n          height corresponds to the visualized metric, providing a clear representation of\n          value distribution and transformation.": [""], "Sankey Chart": [""], "Primary Metric": [""], "The primary metric is used to define the arc segment sizes": [""], "Secondary Metric": [""], "[optional] this secondary metric is used to define the color as a ratio against the primary metric. When omitted, the color is categorical and based on labels": [""], "When only a primary metric is provided, a categorical color scale is used.": [""], "When a secondary metric is provided, a linear color scale is used.": [""], "Hierarchy": [""], "Sets the hierarchy levels of the chart. Each level is\n        represented by one ring with the innermost circle as the top of the hierarchy.": [""], "Uses circles to visualize the flow of data through different stages of a system. Hover over individual paths in the visualization to understand the stages a value took. Useful for multi-stage, multi-group visualizing funnels and pipelines.": [""], "Sunburst Chart": [""], "Multi-Levels": [""], "% of total": [""], "% of parent": [""], "When using other than adaptive formatting, labels may overlap": [""], "Swiss army knife for visualizing data. Choose between step, line, scatter, and bar charts. This viz type has many customization options as well.": [""], "Generic Chart": [""], "zoom area": [""], "restore zoom": [""], "Series Style": [""], "Area chart opacity": [""], "Opacity of Area Chart. Also applies to confidence band.": [""], "Marker Size": [""], "Area charts are similar to line charts in that they represent variables with the same scale, but area charts stack the metrics on top of each other.": [""], "Area Chart": [""], "Axis Title": [""], "AXIS TITLE MARGIN": [""], "AXIS TITLE POSITION": [""], "Axis Format": [""], "Logarithmic axis": [""], "Draw split lines for minor axis ticks": [""], "Truncate Axis": [""], "It’s not recommended to truncate axis in Bar chart.": [""], "Axis Bounds": [""], "Bounds for the axis. When left empty, the bounds are dynamically defined based on the min/max of the data. Note that this feature will only expand the axis range. It won't narrow the data's extent.": [""], "Chart Orientation": [""], "Bar orientation": [""], "Vertical": [""], "Horizontal": [""], "Orientation of bar chart": [""], "Bar Charts are used to show metrics as a series of bars.": [""], "Bar Chart": [""], "Line chart is used to visualize measurements taken over a given category. Line chart is a type of chart which displays information as a series of data points connected by straight line segments. It is a basic type of chart common in many fields.": [""], "Line Chart": [""], "Scatter Plot has the horizontal axis in linear units, and the points are connected in order. It shows a statistical relationship between two variables.": [""], "Scatter Plot": [""], "Smooth-line is a variation of the line chart. Without angles and hard edges, Smooth-line sometimes looks smarter and more professional.": [""], "Step type": [""], "Start": [""], "Middle": [""], "End": [""], "Defines whether the step should appear at the beginning, middle or end between two data points": [""], "Stepped-line graph (also called step chart) is a variation of line chart but with the line forming a series of steps between data points. A step chart can be useful when you want to show the changes that occur at irregular intervals.": [""], "Stepped Line": [""], "Name of the id column": [""], "Parent": [""], "Name of the column containing the id of the parent node": [""], "Optional name of the data column.": [""], "Root node id": [""], "Id of root node of the tree.": [""], "Metric for node values": [""], "Tree layout": [""], "Orthogonal": [""], "Radial": [""], "Layout type of tree": [""], "Tree orientation": [""], "Left to Right": [""], "Right to Left": [""], "Top to Bottom": [""], "Bottom to Top": [""], "Orientation of tree": [""], "Node label position": [""], "left": [""], "top": [""], "right": [""], "bottom": [""], "Position of intermediate node label on tree": [""], "Child label position": [""], "Position of child node label on tree": [""], "Emphasis": [""], "ancestor": [""], "descendant": [""], "Which relatives to highlight on hover": [""], "Symbol": [""], "Empty circle": [""], "Circle": [""], "Rectangle": [""], "Triangle": [""], "Diamond": [""], "Pin": [""], "Arrow": [""], "Symbol size": [""], "Size of edge symbols": [""], "Visualize multiple levels of hierarchy using a familiar tree-like structure.": [""], "Tree Chart": [""], "Show Upper Labels": [""], "Show labels when the node has children.": [""], "Key": [""], "Show hierarchical relationships of data, with the value represented by area, showing proportion and contribution to the whole.": [""], "Treemap": [""], "Total": [""], "Assist": [""], "Increase": [""], "Decrease": [""], "Series colors": [""], "Breaks down the series by the category specified in this control.\n      This can help viewers understand how each category affects the overall value.": [""], "A waterfall chart is a form of data visualization that helps in understanding\n          the cumulative effect of sequentially introduced positive or negative values.\n          These intermediate values can either be time based or category based.": [""], "Waterfall Chart": [""], "page_size.all": [""], "Content with navigation": [""], "Reset view (Esc)": [""], "Reset": [""], "Double-click to reset zoom. Use +/- keys to zoom in/out": [""], "Chart Navigation Features": [""], "This chart supports interactive navigation:": [""], "Pan the chart:": [""], "Hold": [""], "while dragging with the mouse": [""], "Zoom in/out:": [""], "while scrolling the mouse wheel, or use": [""], "and": [""], "keys": [""], "Reset view:": [""], "Press": [""], "or click the Reset button": [""], "Got it!": [""], "This JavaScript code will be executed after the template is rendered.": [""], "You can use it to add event handlers, manipulate DOM elements inside container, etc.": [""], "Define JavaScript code to execute after the template is rendered": [""], "JavaScript Code": [""], "Allow navigation tools": [""], "Enable navigation tools for the handlebars template. Allows panning with middle mouse button or Cmd/Alt+drag, and zooming with Cmd/Alt+mouse wheel.": [""], "Loading...": [""], "Write a handlebars template to render the data": [""], "Handlebars": [""], "must have a value": [""], "Handlebars Template": [""], "A handlebars template that is applied to the data": [""], "Include time": [""], "Whether to include the time granularity as defined in the time section": [""], "Percentage metrics": [""], "Select one or many metrics to display, that will be displayed in the percentages of total. Percentage metrics will be calculated only from data within the row limit. You can use an aggregation function on a column or write custom SQL to create a percentage metric.": [""], "Show summary": [""], "Show total aggregations of selected metrics. Note that row limit does not apply to the result.": [""], "Ordering": [""], "Order results by selected columns": [""], "Sort descending": [""], "Server pagination": [""], "Enable server side pagination of results (experimental feature)": [""], "Server Page Length": [""], "Rows per page, 0 means no pagination": [""], "Query mode": [""], "Group By, Metrics or Percentage Metrics must have a value": [""], "You need to configure HTML sanitization to use CSS": [""], "CSS Styles": [""], "CSS applied to the chart": [""], "Columns to group by on the columns": [""], "Rows": [""], "Columns to group by on the rows": [""], "Apply metrics on": [""], "Use metrics as a top level group for columns or for rows": [""], "Cell limit": [""], "Limits the number of cells that get retrieved.": [""], "Metric used to define how the top series are sorted if a series or cell limit is present. If undefined reverts to the first metric (where appropriate).": [""], "Aggregation function": [""], "Aggregate function to apply when pivoting and computing the total rows and columns": [""], "Show rows total": [""], "Display row level total": [""], "Show rows subtotal": [""], "Display row level subtotal": [""], "Show columns total": [""], "Display column level total": [""], "Show columns subtotal": [""], "Display column level subtotal": [""], "Transpose pivot": [""], "Swap rows and columns": [""], "Combine metrics": [""], "Display metrics side by side within each column, as opposed to each column being displayed side by side for each metric.": [""], "Adaptive formatting dot ddmmyyyy": [""], "D3 time format for datetime columns": [""], "Sort rows by": [""], "key a-z": [""], "key z-a": [""], "value ascending": [""], "value descending": [""], "Change order of rows.": [""], "Available sorting modes:": [""], "By key: use row names as sorting key": [""], "By value: use metric values as sorting key": [""], "Sort columns by": [""], "Change order of columns.": [""], "By key: use column names as sorting key": [""], "Rows subtotal position": [""], "Position of row level subtotal": [""], "Columns subtotal position": [""], "Position of column level subtotal": [""], "Customize columns": [""], "Further customize how to display each column": [""], "Apply conditional color formatting to metrics": [""], "Used to summarize a set of data by grouping together multiple statistics along two axes. Examples: Sales numbers by region and month, tasks by status and assignee, active users by age and location. Not the most visually stunning visualization, but highly informative and versatile.": [""], "Pivot Table": [""], "metric": [""], "Subtotal": [""], "Total (%(aggregatorName)s)": [""], "Value has own aggregation: %(aggregation)s": [""], "Unknown input format": [""], "Search %s records": [""], "search.num_records": [""], "page_size.show": [""], "Show %s entries": [""], "page_size.entries": [""], "No matching records found": [""], "Display all": [""], "Main": [""], "Select columns that will be displayed in the table. You can multiselect columns.": [""], "Shift + Click to sort by multiple columns": [""], "Summary": [""], "Display": [""], "Number formatting": [""], "Export": [""], "Timestamp format": [""], "Page length": [""], "Search box": [""], "Whether to include a client-side search box": [""], "Allow columns to be rearranged": [""], "Allow end user to drag-and-drop column headers to rearrange them. Note their changes won't persist for the next time they open the chart.": [""], "Render columns in HTML format": [""], "Render data in HTML format if applicable.": [""], "Visual formatting": [""], "Show Cell bars": [""], "Whether to display a bar chart background in table columns": [""], "Align +/-": [""], "Whether to align background charts with both positive and negative values at 0": [""], "add colors to cell bars for +/-": [""], "Whether to colorize numeric values by whether they are positive or negative": [""], "basic conditional formatting": [""], "This will be applied to the whole table. Arrows (↑ and ↓) will be added to main columns for increase and decrease. Basic conditional formatting can be overwritten by conditional formatting below.": [""], "color type": [""], "Custom Conditional Formatting": [""], "Apply conditional color formatting to numeric columns": [""], "Classic row-by-column spreadsheet like view of a dataset. Use tables to showcase a view into the underlying data or to show aggregated metrics.": [""], "Show": [""], "entries": [""], "Minimum Font Size": [""], "Font size for the smallest value in the list": [""], "Maximum Font Size": [""], "Font size for the biggest value in the list": [""], "Word Rotation": [""], "random": [""], "square": [""], "Rotation to apply to words in the cloud": [""], "Visualizes the words in a column that appear the most often. Bigger font corresponds to higher frequency.": [""], "Word Cloud": [""], "You cannot edit titles from Dataset": [""], "Editing filter set:": [""], "Cancel": [""], "Filter set with this name already exists": [""], "Filter set already exists": [""], "Save": [""], "This filter set is identical to: \"%s\"": [""], "Edit": [""], "The primary set of filters will be applied automatically": [""], "Set as primary": [""], "Remove invalid filters": [""], "Rebuild": [""], "Delete": [""], "Filters (%d)": [""], "This filter doesn't exist in dashboard. It will not be applied.": [""], "Filter metadata changed in dashboard. It will not be applied.": [""], "Please filter set name": [""], "Create": [""], "Create filter set": [""], "New filter set": [""], "Please apply filter changes": [""], "Unknown value": [""], "Service temporarily unavailable": [""], "Sorry, something went wrong. We are fixing the mistake now. Try again later.": [""], "Edit label colors": [""], "Number of changes": [""], "Deleted": [""], "Not assigned": [""], "Edit colors": [""], "The dashboard has a color scheme applied: ": [""], "You can override the colors of the metrics with your own values.": [""], "Search for label": [""], "Filter labels": [""], "On dashboard": [""], "All": [""], "Metric is missing from the dashboard with current filters or removed from the dataset": [""], "Color": [""], "Present on charts": [""], "No results match your filter criteria": [""], "Edit formatter": [""], "Add new formatter": [""], "Add new color formatter": [""], "Color scheme": [""], "Apply": [""], "success": [""], "alert": [""], "error": [""], "success dark": [""], "alert dark": [""], "error dark": [""], "This value should be smaller than the right target value": [""], "This value should be greater than the left target value": [""], "Required": [""], "Operator": [""], "Left value": [""], "Right value": [""], "Target value": [""], "Select column": [""], "Message": [""], "Create new team": [""], "Create team": [""], "User from": [""], "slug": [""], "New team will be created": [""], "User will update is on the next step": [""], "Next": [""], "Team Name": [""], "Too short": [""], "Too long": [""], "Full Team Name": [""], "enter team name": [""], "You are welcome to Superset": [""], "Tell us more about yourself": [""], "All the data is from Dodo IS. Please enter your team or role. It helps to proceed your request.": [""], "First name": [""], "Last name": [""], "Role in Dodo Brands": [""], "Please input your role in Dodo Brands!": [""], "Minimum 3 characters": [""], "Maximum 30 characters": [""], "Next step": [""], "You request created": [""], "Request will be proceed by administrators. You can see your requests in": [""], "profile": [""], "Tell us why you are here": [""], "Are you a franchisee or from a Managing Company?": [""], "Create of find your team": [""], "Select `C_LEVEL` if you are a ‘C level’ in DODO": [""], "Your team name is": [""], "Finish onboarding": [""], "All C-level people please select ‘c_level’": [""], "your team": [""], "Which use cases are you interested in using Superset for?": [""], "readonly": [""], "Check available dashboards. Gather insights from charts inside a dashboard": [""], "Create dashboards and charts": [""], "Create dashboards. Create charts": [""], "Create datasets and use SQL Lab": [""], "Create datasets. Use SQL Lab for your Ad-hoc queries": [""], "Based on your selection, your roles are:": [""], "An error occurred while checking user's team": [""], "Team search": [""], "Existing team": [""], "Check information and update user": [""], "If this is C-level": [""], "give: c_level": [""], "If it's a franchisee": [""], "Give: fr_{last name}_{first name}": [""], "If from management company": [""], "Give: by the name of the team": [""], "Team has been created successfully.": [""], "An error occurred while creating the team": [""], "Request closed successfully.": [""], "An error occurred while closing the request": [""], "Onboarding request": [""], "Email": [""], "Current roles": [""], "Requested roles": [""], "Closed": [""], "Request date": [""], "Update date": [""], "Check available dashboards": [""], "Gather insights from charts inside a dashboard": [""], "Create datasets": [""], "Use SQL Lab for your Ad-hoc queries": [""], "Create charts": [""], "User update": [""], "Apply roles and link to a team": [""], "New Roles": [""], "Actions": [""], "Requests": [""], "An error occurred while fetching statements user values: %s": [""], "Any": [""], "Franchisee": [""], "Members count": [""], "Add members": [""], "select member": [""], "Members": [""], "Username": [""], "Created on": [""], "Login count": [""], "Last login date": [""], "REMOVE FROM TEAM": [""], "Member added successfully.": [""], "Member removed successfully.": [""], "An error occurred while adding user:": [""], "An error occurred while removing user:": [""], "Teams": [""], "offline": [""], "failed": [""], "pending": [""], "fetching": [""], "running": [""], "stopped": [""], "The query couldn't be loaded": [""], "Your query has been scheduled. To see details of your query, navigate to Saved queries": [""], "Your query could not be scheduled": [""], "Failed at retrieving results": [""], "Query was stopped.": [""], "Failed at stopping query. %s": [""], "Unable to migrate table schema state to backend. Superset will retry later. Please contact your administrator if this problem persists.": [""], "Unable to migrate query state to backend. Superset will retry later. Please contact your administrator if this problem persists.": [""], "Unable to migrate query editor state to backend. Superset will retry later. Please contact your administrator if this problem persists.": [""], "-- Note: Unless you save your query, these tabs will NOT persist if you clear your cookies or change browsers.\n\n": [""], "Copy of %s": [""], "An error occurred while fetching tab state": [""], "An error occurred while removing query. Please contact your administrator.": [""], "Your query could not be saved": [""], "Your query was not properly saved": [""], "Your query was saved": [""], "Your query was updated": [""], "Your query could not be updated": [""], "An error occurred while storing your query in the backend. To avoid losing your changes, please save your query using the \"Save Query\" button.": [""], "An error occurred while fetching table metadata. Please contact your administrator.": [""], "An error occurred while expanding the table schema. Please contact your administrator.": [""], "An error occurred while collapsing the table schema. Please contact your administrator.": [""], "An error occurred while removing the table schema. Please contact your administrator.": [""], "Shared query": [""], "The datasource couldn't be loaded": [""], "An error occurred while creating the data source": [""], "An error occurred while fetching function names.": [""], "SQL Lab uses your browser's local storage to store queries and results.\nCurrently, you are using %(currentUsage)s KB out of %(maxStorage)d KB storage space.\nTo keep SQL Lab from crashing, please delete some query tabs.\nYou can re-access these queries by using the Save feature before you delete the tab.\nNote that you will need to close other SQL Lab windows before you do this.": [""], "Primary key": [""], "Foreign key": [""], "Index": [""], "Estimate selected query cost": [""], "Estimate cost": [""], "Cost estimate": [""], "Creating a data source and creating a new tab": [""], "Explore the result set in the data exploration view": [""], "explore": [""], "Create Chart": [""], "Source SQL": [""], "Executed SQL": [""], "SQL": [""], "Run query": [""], "Run current query": [""], "Stop query": [""], "New tab": [""], "Previous Line": [""], "Format SQL": [""], "Switch to the previous tab": [""], "Switch to the next tab": [""], "Find": [""], "Replace": [""], "Keyboard shortcuts": [""], "Run a query to display query history": [""], "LIMIT": [""], "State": [""], "Started": [""], "Duration": [""], "Results": [""], "Success": [""], "Failed": [""], "Running": [""], "Fetching": [""], "Offline": [""], "Scheduled": [""], "Unknown Status": [""], "View": [""], "Data preview": [""], "Overwrite text in the editor with a query on this table": [""], "Run query in a new tab": [""], "Remove query from log": [""], "Unable to create chart without a query id.": [""], "Save & Explore": [""], "Overwrite & Explore": [""], "Save this query as a virtual dataset to continue exploring": [""], "Download to CSV": [""], "Download to XLSX": [""], "Copy to Clipboard": [""], "Filter results": [""], "The number of results displayed is limited to %(rows)d by the configuration DISPLAY_MAX_ROW. Please add additional limits/filters or download to csv to see more rows up to the %(limit)d limit.": [""], "The number of results displayed is limited to %(rows)d. Please add additional limits/filters, download to csv, or contact an admin to see more rows up to the %(limit)d limit.": [""], "The number of rows displayed is limited to %(rows)d by the query": [""], "The number of rows displayed is limited to %(rows)d by the limit dropdown.": [""], "The number of rows displayed is limited to %(rows)d by the query and limit dropdown.": [""], "%(rows)d rows returned": [""], "The number of rows displayed is limited to %(rows)d by the dropdown.": [""], "Track job": [""], "See query details": [""], "Query was stopped": [""], "Database error": [""], "Retry fetching results": [""], "was created": [""], "Query in a new tab": [""], "The query returned no data": [""], "Fetch data preview": [""], "Refetch results": [""], "Stop": [""], "Run selection": [""], "Run": [""], "Stop running (Ctrl + x)": [""], "Stop running (Ctrl + e)": [""], "Run query (Ctrl + Return)": [""], "Untitled Dataset": [""], "An error occurred saving dataset": [""], "Save or Overwrite Dataset": [""], "Back": [""], "Save as new": [""], "Overwrite existing": [""], "Select or type dataset name": [""], "Existing dataset": [""], "Are you sure you want to overwrite this dataset?": [""], "Undefined": [""], "Save dataset": [""], "Save as": [""], "Save query": [""], "Update": [""], "Label for your query": [""], "Write a description for your query": [""], "Submit": [""], "Schedule query": [""], "Schedule": [""], "There was an error with your request": [""], "Please save the query to enable sharing": [""], "Copy query link to your clipboard": [""], "Save the query to enable this feature": [""], "Copy link": [""], "Run a query to display results": [""], "No stored results found, you need to re-run your query": [""], "Query history": [""], "Preview: `%s`": [""], "Schedule the query periodically": [""], "You must run the query successfully first": [""], "Render HTML": [""], "Autocomplete": [""], "CREATE TABLE AS": [""], "CREATE VIEW AS": [""], "The database that was used to generate this query could not be found": [""], "Choose one of the available databases on the left panel.": [""], "Estimate the cost before running a query": [""], "Specify name to CREATE VIEW AS schema in: public": [""], "Specify name to CREATE TABLE AS schema in: public": [""], "Select a database to write a query": [""], "Choose one of the available databases from the panel on the left.": [""], "Collapse table preview": [""], "Expand table preview": [""], "Reset state": [""], "Enter a new title for the tab": [""], "Close tab": [""], "Rename tab": [""], "Expand tool bar": [""], "Hide tool bar": [""], "Close all other tabs": [""], "Duplicate tab": [""], "Add a new tab": [""], "New tab (Ctrl + q)": [""], "New tab (Ctrl + t)": [""], "Add a new tab to create SQL Query": [""], "An error occurred while fetching table metadata": [""], "Copy partition query to clipboard": [""], "latest partition:": [""], "Keys for table": [""], "View keys & indexes (%s)": [""], "Original table column order": [""], "Sort columns alphabetically": [""], "Copy SELECT statement to the clipboard": [""], "Show CREATE VIEW statement": [""], "CREATE VIEW statement": [""], "Remove table preview": [""], "Assign a set of parameters as": [""], "below (example:": [""], "), and they become available in your SQL (example:": [""], "by using": [""], "Jinja templating": [""], "syntax.": [""], "Edit template parameters": [""], "Parameters ": [""], "Invalid JSON": [""], "Untitled query": [""], "%s%s": [""], "Control": [""], "Before": [""], "After": [""], "Click to see difference": [""], "Altered": [""], "Chart changes": [""], "Modified by: %s": [""], "Loaded data cached": [""], "Loaded from cache": [""], "Click to force-refresh": [""], "Cached": [""], "Waiting on %s": [""], "Waiting on database...": [""], "Add required control values to preview chart": [""], "Your chart is ready to go!": [""], "Click on \"Create chart\" button in the control panel on the left to preview a visualization or": [""], "click here": [""], "No results were returned for this query": [""], "Make sure that the controls are configured properly and the datasource contains data for the selected time range": [""], "An error occurred while loading the SQL": [""], "Sorry, an error occurred": [""], "Updating chart was stopped": [""], "An error occurred while rendering the visualization: %s": [""], "Network error.": [""], "Cross-filter will be applied to all of the charts that use this dataset.": [""], "You can also just click on the chart to apply cross-filter.": [""], "Cross-filtering is not enabled for this dashboard.": [""], "This visualization type does not support cross-filtering.": [""], "You can't apply cross-filter on this data point.": [""], "Remove cross-filter": [""], "Add cross-filter": [""], "Failed to load dimensions for drill by": [""], "Drill by is not yet supported for this chart type": [""], "Drill by is not available for this data point": [""], "Drill by": [""], "Search columns": [""], "No columns found": [""], "Failed to generate chart edit URL": [""], "You do not have sufficient permissions to edit the chart": [""], "Edit chart": [""], "Close": [""], "Failed to load chart data.": [""], "Drill by: %s": [""], "There was an error loading the chart data": [""], "Results %s": [""], "Drill to detail": [""], "Drill to detail by": [""], "Drill to detail is disabled for this database. Change the database settings to enable it.": [""], "Drill to detail is disabled because this chart does not group data by dimension value.": [""], "Right-click on a dimension value to drill to detail by that value.": [""], "Drill to detail by value is not yet supported for this chart type.": [""], "Drill to detail: %s": [""], "Formatting": [""], "Formatted value": [""], "No rows were returned for this dataset": [""], "Reload": [""], "Copy": [""], "Copy to clipboard": [""], "Copied to clipboard!": [""], "Sorry, your browser does not support copying. Use Ctrl / Cmd + C!": [""], "every": [""], "every month": [""], "every day of the month": [""], "day of the month": [""], "every day of the week": [""], "day of the week": [""], "every hour": [""], "every minute": [""], "minute": [""], "reboot": [""], "Every": [""], "in": [""], "on": [""], "or": [""], "at": [""], ":": [""], "minute(s)": [""], "Invalid cron expression": [""], "Clear": [""], "Sunday": [""], "Monday": [""], "Tuesday": [""], "Wednesday": [""], "Thursday": [""], "Friday": [""], "Saturday": [""], "January": [""], "February": [""], "March": [""], "April": [""], "May": [""], "June": [""], "July": [""], "August": [""], "September": [""], "October": [""], "November": [""], "December": [""], "SUN": [""], "MON": [""], "TUE": [""], "WED": [""], "THU": [""], "FRI": [""], "SAT": [""], "JAN": [""], "FEB": [""], "MAR": [""], "APR": [""], "MAY": [""], "JUN": [""], "JUL": [""], "AUG": [""], "SEP": [""], "OCT": [""], "NOV": [""], "DEC": [""], "There was an error loading the schemas": [""], "There was an error loading the catalogs": [""], "Select database or type to search databases": [""], "Force refresh catalog list": [""], "Select catalog or type to search catalogs": [""], "Catalog": [""], "No compatible catalog found": [""], "Force refresh schema list": [""], "Select schema or type to search schemas": [""], "No compatible schema found": [""], "Warning! Changing the dataset may break the chart if the metadata does not exist.": [""], "Changing the dataset may break the chart if the chart relies on columns or metadata that does not exist in the target dataset": [""], "dataset": [""], "Successfully changed dataset!": [""], "Connection": [""], "Swap dataset": [""], "Proceed": [""], "Warning!": [""], "Search / Filter": [""], "Add item": [""], "STRING": [""], "NUMERIC": [""], "DATETIME": [""], "BOOLEAN": [""], "Physical (table or view)": [""], "Virtual (SQL)": [""], "Label (Eng)": [""], "Label (Rus)": [""], "Data type": [""], "Advanced data type": [""], "Advanced Data type": [""], "Datetime format": [""], "The pattern of timestamp format. For strings use ": [""], "Python datetime string pattern": [""], " expression which needs to adhere to the ": [""], "ISO 8601": [""], " standard to ensure that the lexicographical ordering\n                      coincides with the chronological ordering. If the\n                      timestamp format does not adhere to the ISO 8601 standard\n                      you will need to define an expression and type for\n                      transforming the string into a date or timestamp. Note\n                      currently time zones are not supported. If time is stored\n                      in epoch format, put `epoch_s` or `epoch_ms`. If no pattern\n                      is specified we fall back to using the optional defaults on a per\n                      database/column name level via the extra parameter.": [""], "Certified By": [""], "Person or group that has certified this metric": [""], "Certified by": [""], "Certification details": [""], "Details of the certification": [""], "Is dimension": [""], "Default datetime": [""], "Is filterable": [""], "<new column>": [""], "Select owners": [""], "Modified columns: %s": [""], "Removed columns: %s": [""], "New columns added: %s": [""], "Metadata has been synced": [""], "An error has occurred": [""], "Column name [%s] is duplicated": [""], "Metric name [%s] is duplicated": [""], "Calculated column [%s] requires an expression": [""], "Invalid currency code in saved metrics": [""], "Basic": [""], "Default URL": [""], "Default URL to redirect to when accessing from the dataset list page": [""], "Autocomplete filters": [""], "Whether to populate autocomplete filters options": [""], "Autocomplete query predicate": [""], "When using \"Autocomplete filters\", this can be used to improve performance of the query fetching the values. Use this option to apply a predicate (WHERE clause) to the query selecting the distinct values from the table. Typically the intent would be to limit the scan by applying a relative time filter on a partitioned or indexed time-related field.": [""], "Extra data to specify table metadata. Currently supports metadata of the format: `{ \"certification\": { \"certified_by\": \"Data Platform Team\", \"details\": \"This table is the source of truth.\" }, \"warning_markdown\": \"This is a warning.\" }`.": [""], "Cache timeout": [""], "The duration of time in seconds before the cache is invalidated. Set to -1 to bypass the cache.": [""], "Hours offset": [""], "The number of hours, negative or positive, to shift the time column. This can be used to move UTC time to local time.": [""], "Normalize column names": [""], "Always filter main datetime column": [""], "When the secondary temporal columns are filtered, apply the same filter to the main datetime column.": [""], "<new spatial>": [""], "<no type>": [""], "Click the lock to make changes.": [""], "Click the lock to prevent further changes.": [""], "virtual": [""], "Dataset name": [""], "When specifying SQL, the datasource acts as a view. Superset will use this statement as a subquery while grouping and filtering on the generated parent queries.": [""], "Physical": [""], "The pointer to a physical table (or view). Keep in mind that the chart is associated to this Superset logical table, and this logical table points the physical table referenced here.": [""], "This field is used as a unique identifier to attach the metric to charts. It is also used as the alias in the SQL query.": [""], "D3 format": [""], "Metric currency": [""], "Select or type currency symbol": [""], "Warning": [""], "Optional warning about use of this metric": [""], "<new metric>": [""], "Be careful.": [""], "Changing these settings will affect all charts using this dataset, including charts owned by other people.": [""], "Sync columns from source": [""], "Calculated columns": [""], "This field is used as a unique identifier to attach the calculated dimension to charts. It is also used as the alias in the SQL query.": [""], "<enter SQL expression here>": [""], "Settings": [""], "The dataset has been saved": [""], "Error saving dataset": [""], "The dataset configuration exposed here\n                affects all the charts using this dataset.\n                Be mindful that changing settings\n                here may affect other charts\n                in undesirable ways.": [""], "Are you sure you want to save and apply changes?": [""], "Confirm save": [""], "OK": [""], "Edit Dataset ": [""], "Use legacy datasource editor": [""], "This dataset is managed externally, and can't be edited in Superset": [""], "DELETE": [""], "Type \"%s\" to confirm": [""], "More": [""], "Click to edit": [""], "You don't have the rights to alter this title.": [""], "No databases match your search": [""], "There are no databases available": [""], "Manage your databases": [""], "here": [""], "Unexpected error": [""], "This may be triggered by:": [""], "%(message)s\nThis may be triggered by: \n%(issues)s": [""], "%s Error": [""], "Missing dataset": [""], "See more": [""], "See less": [""], "Copy message": [""], "Details": [""], "Authorization needed": [""], "Did you mean:": [""], "Parameter error": [""], "%(subtitle)s\nThis may be triggered by:\n %(issue)s": [""], "Timeout error": [""], "Click to favorite/unfavorite": [""], "Cell content": [""], "Hide password.": [""], "Show password.": [""], "Database driver for importing maybe not installed. Visit the Superset documentation page for installation instructions: ": [""], "OVERWRITE": [""], "Database passwords": [""], "%s PASSWORD": [""], "%s SSH TUNNEL PASSWORD": [""], "%s SSH TUNNEL PRIVATE KEY": [""], "%s SSH TUNNEL PRIVATE KEY PASSWORD": [""], "Overwrite": [""], "Import": [""], "Import %s": [""], "Select file": [""], "Last Updated %s": [""], "Sort": [""], "+ %s more": [""], "%s Selected": [""], "Deselect all": [""], "Add Tag": [""], "Try different criteria to display results.": [""], "clear all filters": [""], "No Data": [""], "%s-%s of %s": [""], "Start date": [""], "End date": [""], "Type a value": [""], "Filter": [""], "Select or type a value": [""], "Last modified": [""], "Modified by": [""], "Created by": [""], "Tags": [""], "Menu actions trigger": [""], "Select ...": [""], "Filter menu": [""], "No filters": [""], "Select all items": [""], "Search in filters": [""], "Select current page": [""], "Invert current page": [""], "Clear all data": [""], "Select all data": [""], "Expand row": [""], "Collapse row": [""], "Click to sort descending": [""], "Click to sort ascending": [""], "Click to cancel sorting": [""], "List updated": [""], "There was an error loading the tables": [""], "See table schema": [""], "Select table or type to search tables": [""], "Force refresh table list": [""], "You do not have permission to read tags": [""], "Timezone selector": [""], "Failed to save cross-filter scoping": [""], "Dashboard label colors updated": [""], "Failed to save dashboard label colors": [""], "There is not enough space for this component. Try decreasing its width, or increasing the destination width.": [""], "Can not move top level tab into nested tabs": [""], "This chart has been moved to a different filter scope.": [""], "There was an issue fetching the favorite status of this dashboard.": [""], "There was an issue favoriting this dashboard.": [""], "This dashboard is now published": [""], "This dashboard is now hidden": [""], "You do not have permissions to edit this dashboard.": [""], "This dashboard was saved successfully.": [""], "Sorry, an unknown error occurred": [""], "Sorry, there was an error saving this dashboard: %s": [""], "You do not have permission to edit this dashboard": [""], "Please confirm the overwrite values.": [""], "You have used all %(historyLength)s undo slots and will not be able to fully undo subsequent actions. You may save your current state to reset the history.": [""], "Could not fetch all saved charts": [""], "Sorry there was an error fetching saved charts: ": [""], "Any color palette selected here will override the colors applied to this dashboard's individual charts": [""], "You have unsaved changes.": [""], "Drag and drop components and charts to the dashboard": [""], "You can create a new chart or use existing ones from the panel on the right": [""], "Create a new chart": [""], "Drag and drop components to this tab": [""], "There are no components added to this tab": [""], "You can add the components in the edit mode.": [""], "There is no chart definition associated with this component, could it have been deleted?": [""], "Delete this container and save to remove this message.": [""], "Refresh interval saved": [""], "Custom interval": [""], "Put positive values and valid minute and second value less than 60": [""], "Put some positive value greater than 0": [""], "Refresh interval": [""], "Refresh frequency": [""], "HOUR": [""], "Type a number": [""], "MINUTE": [""], "Minutes value": [""], "minutes": [""], "SECOND": [""], "Seconds value": [""], "seconds": [""], "Are you sure you want to proceed?": [""], "Save for this session": [""], "You must pick a name for the new dashboard": [""], "Save dashboard": [""], "Overwrite Dashboard [%s]": [""], "Save as:": [""], "[dashboard name]": [""], "also copy (duplicate) charts": [""], "viz type": [""], "recent": [""], "Create new chart": [""], "Filter your charts": [""], "Filter charts": [""], "Sort by %s": [""], "Show only my charts": [""], "You can choose to display all charts that you have access to or only the ones you own.\n              Your filter selection will be saved and remain active until you choose to change it.": [""], "Added": [""], "Unknown type": [""], "Viz type": [""], "Dataset": [""], "Superset chart": [""], "Check out this chart in dashboard:": [""], "Layout elements": [""], "An error occurred while fetching available CSS templates": [""], "Load a CSS template": [""], "Live CSS editor": [""], "Collapse tab content": [""], "There are no charts added to this dashboard": [""], "Go to the edit mode to configure the dashboard and add charts": [""], "The following filters have the 'Select first filter value by default'\n                    option checked and could not be loaded, which is preventing the dashboard\n                    from rendering: %s": [""], "Processing file export...": [""], "Changes saved.": [""], "Disable embedding?": [""], "This will remove your current embed configuration.": [""], "Embedding deactivated.": [""], "Sorry, something went wrong. Embedding could not be deactivated.": [""], "Sorry, something went wrong. Please try again.": [""], "This dashboard is ready to embed. In your application, pass the following id to the SDK:": [""], "Configure this dashboard to embed it into an external web application.": [""], "For further instructions, consult the": [""], "Superset Embedded SDK documentation.": [""], "Allowed Domains (comma separated)": [""], "A list of domain names that can embed this dashboard. Leaving this field empty will allow embedding from any domain.": [""], "Deactivate": [""], "Save changes": [""], "Enable embedding": [""], "Embed": [""], "Applied filters (%s)": [""], "Applied cross-filters (%d)": [""], "Applied filters (%d)": [""], "This dashboard is currently auto refreshing; the next auto refresh will be in %s.": [""], "Your dashboard is too large. Please reduce its size before saving it.": [""], "Not available": [""], "Add the name of the dashboard": [""], "Dashboard title": [""], "Undo the action": [""], "Redo the action": [""], "Discard": [""], "Refreshing charts": [""], "Superset dashboard": [""], "Check out this dashboard: ": [""], "Refresh dashboard": [""], "Exit fullscreen": [""], "Enter fullscreen": [""], "Edit properties": [""], "Edit CSS": [""], "Download": [""], "Export to PDF": [""], "Download as Image": [""], "Share": [""], "Copy permalink to clipboard": [""], "Share permalink by email": [""], "Manage email report": [""], "Set filter mapping": [""], "Set auto-refresh interval": [""], "Confirm overwrite": [""], "Scroll down to the bottom to enable overwriting changes. ": [""], "Yes, overwrite changes": [""], "Are you sure you intend to overwrite the following values?": [""], "Last Updated %s by %s": [""], "Error": [""], "A valid color scheme is required": [""], "JSON metadata is invalid!": [""], "Dashboard properties updated": [""], "The dashboard has been saved": [""], "Access": [""], "Owners is a list of users who can alter the dashboard. Searchable by name or username.": [""], "Colors": [""], "Roles is a list which defines access to the dashboard. Granting a role access to a dashboard will bypass dataset level checks. If no roles are defined, regular access permissions apply.": [""], "Dashboard properties": [""], "This dashboard is managed externally, and can't be edited in Superset": [""], "Basic information": [""], "Title (Eng)": [""], "Title (Rus)": [""], "URL slug": [""], "A readable URL for your dashboard": [""], "Certification": [""], "Person or group that has certified this dashboard.": [""], "Any additional detail to show in the certification tooltip.": [""], "A list of tags that have been applied to this chart.": [""], "You can now edit the colors of the metrics in a separate modal window on the dashboard by clicking on the ‘Edit label colours’ button.": [""], "JSON metadata": [""], "Please DO NOT overwrite the \"filter_scopes\" key.": [""], "Use \"%(menuName)s\" menu instead.": [""], "This dashboard is not published, it will not show up in the list of dashboards. Click here to publish this dashboard.": [""], "This dashboard is not published which means it will not show up in the list of dashboards. Favorite it to see it there or access it by using the URL directly.": [""], "This dashboard is published. Click to make it a draft.": [""], "Draft": [""], "Annotation layers are still loading.": [""], "One ore more annotation layers failed loading.": [""], "This chart applies cross-filters to charts whose datasets contain columns with the same name.": [""], "Data refreshed": [""], "Cached %s": [""], "Fetched %s": [""], "Query %s: %s": [""], "Force refresh": [""], "Hide chart description": [""], "Show chart description": [""], "Cross-filtering scoping": [""], "View query": [""], "View as table": [""], "Chart Data: %s": [""], "Share chart by email": [""], "Check out this chart: ": [""], "Export to .CSV": [""], "Export to Excel": [""], "Export to full .CSV": [""], "Export to full Excel": [""], "Download as image": [""], "Something went wrong.": [""], "Search...": [""], "No filter is selected.": [""], "Editing 1 filter:": [""], "Batch editing %d filters:": [""], "Configure filter scopes": [""], "There are no filters in this dashboard.": [""], "Expand all": [""], "Collapse all": [""], "An error occurred while opening Explore": [""], "Empty column": [""], "This markdown component has an error.": [""], "This markdown component has an error. Please revert your recent changes.": [""], "Empty row": [""], "You can": [""], "create a new chart": [""], "or use existing ones from the panel on the right": [""], "You can add the components in the": [""], "edit mode": [""], "Delete dashboard tab?": [""], "Deleting a tab will remove all content within it. You may still reverse this action with the": [""], "undo": [""], "button (cmd + z) until you save your changes.": [""], "CANCEL": [""], "Divider": [""], "Header": [""], "Text / Markdown": [""], "Tabs": [""], "background": [""], "Preview": [""], "Sorry, something went wrong. Try again later.": [""], "The screenshot is being generated. Please, do not leave the page.": [""], "The screenshot could not be downloaded. Please, try again later.": [""], "The screenshot has been downloaded.": [""], "Add/Edit Filters": [""], "No filters are currently added to this dashboard.": [""], "No global filters are currently added": [""], "Click on \"+Add/Edit Filters\" button to create new dashboard filters": [""], "All filters (%(filterCount)d)": [""], "Filter sets (%(filterSetCount)d)": [""], "Apply filters": [""], "Clear all": [""], "Locate the chart": [""], "Cross-filters": [""], "Add custom scoping": [""], "All charts/global scoping": [""], "Cross-filtering is not enabled in this dashboard": [""], "Select the charts to which you want to apply cross-filters when interacting with this chart. You can select \"All charts\" to apply filters to all charts that use the same dataset or contain the same column name in the dashboard.": [""], "Select the charts to which you want to apply cross-filters in this dashboard. Deselecting a chart will exclude it from being filtered when applying cross-filters from any chart on the dashboard. You can select \"All charts\" to apply cross-filters to all charts that use the same dataset or contain the same column name in the dashboard.": [""], "All charts": [""], "Enable cross-filtering": [""], "Orientation of filter bar": [""], "Vertical (Left)": [""], "Horizontal (Top)": [""], "More filters": [""], "No applied filters": [""], "Applied filters: %s": [""], "Cannot load filter": [""], "Filters out of scope (%d)": [""], "Dependent on": [""], "Filter only displays values relevant to selections made in other filters.": [""], "Scope": [""], "Filter type": [""], "Title is required": [""], "(Removed)": [""], "Undo?": [""], "Add filters and dividers": [""], "[untitled]": [""], "Cyclic dependency detected": [""], "Add and edit filters": [""], "Column select": [""], "Select a column": [""], "No compatible columns found": [""], "No compatible datasets found": [""], "Value is required": [""], "(deleted or invalid type)": [""], "Limit type": [""], "No available filters.": [""], "Add filter": [""], "Values are dependent on other filters": [""], "Values selected in other filters will affect the filter options to only show relevant values": [""], "Values dependent on": [""], "Scoping": [""], "Filter Configuration": [""], "Filter Settings": [""], "Select filter": [""], "Range filter": [""], "Numerical range": [""], "Time filter": [""], "Time range": [""], "Time column": [""], "Time grain": [""], "Group By": [""], "Group by": [""], "Select by id filter": [""], "Filter by ID": [""], "Select with translation": [""], "Value with translation": [""], "Select by id with translation": [""], "Filter by ID with translation": [""], "This filter is used if there are two columns with values in different languages. For example, Delivery and Доставка": [""], "Dashboard time range filters apply to temporal columns in chart filters. Add temporal columns to apply the dashboard filter": [""], "Pre-filter is required": [""], "Time column to apply dependent temporal filter to": [""], "Time column to apply time range to": [""], "Name is required": [""], "Filter Type": [""], "Datasets do not contain a temporal column": [""], "Filter name": [""], "Dataset is required": [""], "Use it with caution": [""], "Not a valid integer": [""], "Pre-filter available values": [""], "Add filter clauses to control the filter's source query,\n                    though only in the context of the autocomplete i.e., these conditions\n                    do not impact how the filter is applied to the dashboard. This is useful\n                    when you want to improve the query's performance by only scanning a subset\n                    of the underlying data or limit the available values displayed in the filter.": [""], "Pre-filter": [""], "No filter": [""], "Sort filter values": [""], "Sort type": [""], "Sort ascending": [""], "Sort Metric": [""], "If a metric is specified, sorting will be done based on the metric value": [""], "Sort metric": [""], "Single Value": [""], "Single value type": [""], "Exact": [""], "Filter has default value": [""], "Default Value": [""], "Default value is required": [""], "Refresh the default values": [""], "Fill all required fields to enable \"Default Value\"": [""], "You have removed this filter.": [""], "Restore Filter": [""], "ColumnId": [""], "Column is required": [""], "Populate \"Default value\" to enable this control": [""], "Default value set automatically when \"Select first filter value by default\" is checked": [""], "Default value must be set when \"Filter value is required\" is checked": [""], "Default value must be set when \"Filter has default value\" is checked": [""], "Apply to all panels": [""], "Apply to specific panels": [""], "Only selected panels will be affected by this filter": [""], "All panels with this column will be affected by this filter": [""], "All panels": [""], "This chart might be incompatible with the filter (datasets don't match)": [""], "Keep editing": [""], "Yes, cancel": [""], "There are unsaved changes.": [""], "Are you sure you want to cancel?": [""], "Error loading chart datasources. Filters may not work correctly.": [""], "Error loading chart filter sets. Primary filter set won't be applied.": [""], "Transparent": [""], "White": [""], "All filters": [""], "Click to edit %s.": [""], "Click to edit chart.": [""], "Use %s to open in a new tab.": [""], "Medium": [""], "New header": [""], "Tab title": [""], "This page is intended to be embedded in an iframe, but it looks like that is not the case.": [""], "This session has encountered an interruption, and some controls may not work as intended. If you are the developer of this app, please check that the guest token is being generated correctly.": [""], "Something went wrong with embedded authentication. Check the dev console for details.": [""], "Equal to (=)": [""], "Not equal to (≠)": [""], "Less than (<)": [""], "Less or equal (<=)": [""], "Greater than (>)": [""], "Greater or equal (>=)": [""], "In": [""], "Not in": [""], "Like": [""], "Like (case insensitive)": [""], "Is not null": [""], "Is null": [""], "use latest_partition template": [""], "Is true": [""], "Is false": [""], "TEMPORAL_RANGE": [""], "Time granularity": [""], "One or many columns to group by. High cardinality groupings should include a series limit to limit the number of fetched and rendered series.": [""], "One or many metrics to display": [""], "Fixed color": [""], "Right axis metric": [""], "Choose a metric for right axis": [""], "Linear color scheme": [""], "Color metric": [""], "One or many controls to pivot as columns": [""], "The time granularity for the visualization. Note that you can type and use simple natural language as in `10 seconds`,`1 day` or `56 weeks`": [""], "The time granularity for the visualization. This applies a date transformation to alter your time column and defines a new time granularity. The options here are defined on a per database engine basis in the Superset source code.": [""], "The time range for the visualization. All relative times, e.g. \"Last month\", \"Last 7 days\", \"now\", etc. are evaluated on the server using the server's local time (sans timezone). All tooltips and placeholder times are expressed in UTC (sans timezone). The timestamps are then evaluated by the database using the engine's local timezone. Note one can explicitly set the timezone per the ISO 8601 format if specifying either the start and/or end time.": [""], "Limits the number of rows that get displayed.": [""], "Metric used to define how the top series are sorted if a series or row limit is present. If undefined reverts to the first metric (where appropriate).": [""], "Defines the grouping of entities. Each series is shown as a specific color on the chart and has a legend toggle": [""], "Metric assigned to the [X] axis": [""], "Metric assigned to the [Y] axis": [""], "Bubble size": [""], "When `Calculation type` is set to \"Percentage change\", the Y Axis Format is forced to `.1%`": [""], "An error occurred while starring this chart": [""], "Chart [%s] has been saved": [""], "Chart [%s] has been overwritten": [""], "Dashboard [%s] just got created and chart [%s] was added to it": [""], "Chart [%s] was added to dashboard [%s]": [""], "GROUP BY": [""], "Use this section if you want a query that aggregates": [""], "NOT GROUPED BY": [""], "Use this section if you want to query atomic rows": [""], "Advanced analytics post processing": [""], "Shift start date": [""], "The X-axis is not on the filters list": [""], "The X-axis is not on the filters list which will prevent it from being used in\n            time range filters in dashboards. Would you like to add it to the filters list?": [""], "You cannot delete the last temporal filter as it's used for time range filters in dashboards.": [""], "This section contains validation errors": [""], "Keep control settings?": [""], "You've changed datasets. Any controls with data (columns, metrics) that match this new dataset have been retained.": [""], "Continue": [""], "Clear form": [""], "No form settings were maintained": [""], "We were unable to carry over any controls when switching to this new dataset.": [""], "Data": [""], "Customize": [""], "Generating link, please wait..": [""], "Chart height": [""], "Chart width": [""], "An error occurred while loading dashboard information.": [""], "Save (Overwrite)": [""], "Save as...": [""], "Chart name": [""], "Dataset Name": [""], "A reusable dataset will be saved with your chart.": [""], "Add to dashboard": [""], "Select": [""], " a dashboard OR ": [""], "create": [""], " a new one": [""], "A new chart and dashboard will be created.": [""], "A new chart will be created.": [""], "A new dashboard will be created.": [""], "Save & go to dashboard": [""], "Save chart": [""], "Formatted date": [""], "Column Formatting": [""], "Collapse data panel": [""], "Expand data panel": [""], "Samples": [""], "No samples were returned for this dataset": [""], "No results": [""], "Showing %s of %s": [""], "%s ineligible item(s) are hidden": [""], "Show less...": [""], "Show all...": [""], "Search Metrics & Columns": [""], " to edit or add columns and metrics.": [""], "Unable to retrieve dashboard colors": [""], "Not added to any dashboard": [""], "You can preview the list of dashboards in the chart settings dropdown.": [""], "Add the name of the chart": [""], "Chart title": [""], "Add required control values to save chart": [""], "Chart type requires a dataset": [""], "This chart type is not supported when using an unsaved query as a chart source. ": [""], " to visualize your data.": [""], "Required control values have been removed": [""], "Your chart is not up to date": [""], "You updated the values in the control panel, but the chart was not updated automatically. Run the query by clicking on the \"Update chart\" button or": [""], "Controls labeled ": [""], "Control labeled ": [""], "Chart Source": [""], "Open Datasource tab": [""], "Original": [""], "You do not have permission to edit this chart": [""], "Chart properties updated": [""], "Edit Chart Properties": [""], "This chart is managed externally, and can't be edited in Superset": [""], "The description can be displayed as widget headers in the dashboard view. Supports markdown.": [""], "Person or group that has certified this chart.": [""], "Configuration": [""], "Duration (in seconds) of the caching timeout for this chart. Set to -1 to bypass the cache. Note this defaults to the dataset's timeout if undefined.": [""], "A list of users who can alter the chart. Searchable by name or username.": [""], "The row limit set for the chart was reached. The chart may show partial data.": [""], "Create chart": [""], "Update chart": [""], "Actual range for comparison": [""], "Invalid lat/long configuration.": [""], "Reverse lat/long ": [""], "Longitude & Latitude columns": [""], "Delimited long & lat single column": [""], "Multiple formats accepted, look the geopy.points Python library for more details": [""], "Geohash": [""], "textarea": [""], "in modal": [""], "Sorry, An error occurred": [""], "Save as Dataset": [""], "Open in SQL Lab": [""], "Failed to verify select options: %s": [""], "Annotation layer": [""], "Select the Annotation Layer you would like to use.": [""], "Use another existing chart as a source for annotations and overlays.\n          Your chart must be one of these visualization types: [%s]": [""], "Expects a formula with depending time parameter 'x'\n        in milliseconds since epoch. mathjs is used to evaluate the formulas.\n        Example: '2x+5'": [""], "Annotation layer value": [""], "Bad formula.": [""], "Annotation Slice Configuration": [""], "This section allows you to configure how to use the slice\n              to generate annotations.": [""], "Annotation layer time column": [""], "Interval start column": [""], "Event time column": [""], "This column must contain date/time information.": [""], "Annotation layer interval end": [""], "Interval End column": [""], "Annotation layer title column": [""], "Title Column": [""], "Pick a title for you annotation.": [""], "Annotation layer description columns": [""], "Description Columns": [""], "Pick one or more columns that should be shown in the annotation. If you don't select a column all of them will be shown.": [""], "Override time range": [""], "This controls whether the \"time_range\" field from the current\n                  view should be passed down to the chart containing the annotation data.": [""], "Override time grain": [""], "This controls whether the time grain field from the current\n                  view should be passed down to the chart containing the annotation data.": [""], "Time delta in natural language\n                  (example:  24 hours, 7 days, 56 weeks, 365 days)": [""], "Display configuration": [""], "Configure your how you overlay is displayed here.": [""], "Annotation layer stroke": [""], "Style": [""], "Solid": [""], "Dashed": [""], "Long dashed": [""], "Dotted": [""], "Annotation layer opacity": [""], "Automatic Color": [""], "Shows or hides markers for the time series": [""], "Hide Line": [""], "Hides the Line for the time series": [""], "Layer configuration": [""], "Configure the basics of your Annotation Layer.": [""], "Mandatory": [""], "Hide layer": [""], "Show label": [""], "Whether to always show the annotation label": [""], "Annotation layer type": [""], "Choose the annotation layer type": [""], "Annotation source type": [""], "Choose the source of your annotations": [""], "Annotation source": [""], "Remove": [""], "Time series": [""], "Edit annotation layer": [""], "Add annotation layer": [""], "Empty collection": [""], "Add an item": [""], "Remove item": [""], "The colors of this chart might be overridden by custom label colors of the related dashboard.\n    Check the JSON metadata in the Advanced settings.": [""], "The color scheme is determined by the related dashboard.\n        Edit the color scheme in the dashboard properties.": [""], "You are viewing this chart in a dashboard context with labels shared across multiple charts.\n        The color scheme selection is disabled.": [""], "You are viewing this chart in the context of a dashboard that is directly affecting its colors.\n        To edit the color scheme, open this chart outside of the dashboard.": [""], "Dashboard scheme": [""], "Custom color palettes": [""], "Featured color palettes": [""], "Other color palettes": [""], "Select color scheme": [""], "Select scheme": [""], "Show less columns": [""], "Show all columns": [""], "This metric will be exported as time": [""], "Fraction digits": [""], "Number of decimal digits to round numbers to": [""], "Min Width": [""], "Default minimal column width in pixels, actual width may still be larger than this if other columns don't need much space": [""], "Text align": [""], "Horizontal alignment": [""], "Show cell bars": [""], "Whether to align positive and negative values in cell bar chart at 0": [""], "Color +/-": [""], "Whether to colorize numeric values by if they are positive or negative": [""], "Truncate Cells": [""], "Truncate long cells to the \"min width\" set above": [""], "Pin column": [""], "Pin column with horizontal scroll": [""], "Select aggregate options": [""], "%s aggregates(s)": [""], "Hide value in Total": [""], "Customize chart metrics or columns with currency symbols as prefixes or suffixes. Choose a symbol from dropdown or type your own.": [""], "Small number format": [""], "D3 number format for numbers between -1.0 and 1.0, useful when you want to have different significant digits for small and large numbers": [""], "No gradient": [""], "Color: ": [""], "Lower threshold must be lower than upper threshold": [""], "Upper threshold must be greater than lower threshold": [""], "Isoline": [""], "Threshold": [""], "Defines the value that determines the boundary between different regions or levels in the data ": [""], "The width of the Isoline in pixels": [""], "The color of the isoline": [""], "Isoband": [""], "Lower Threshold": [""], "The lower limit of the threshold range of the Isoband": [""], "Upper Threshold": [""], "The upper limit of the threshold range of the Isoband": [""], "The color of the isoband": [""], "Click to add a contour": [""], "Prefix": [""], "Suffix": [""], "Currency prefix or suffix": [""], "Prefix or suffix": [""], "Currency symbol": [""], "Currency": [""], "Edit dataset": [""], "You must be a dataset owner in order to edit. Please reach out to a dataset owner to request modifications or edit access.": [""], "View in SQL Lab": [""], "Query preview": [""], "Save as dataset": [""], "Missing URL parameters": [""], "The URL is missing the dataset_id or slice_id parameters.": [""], "The dataset linked to this chart may have been deleted.": [""], "RANGE TYPE": [""], "Actual time range": [""], "APPLY": [""], "Edit time range": [""], "Configure Advanced Time Range ": [""], "START (INCLUSIVE)": [""], "Start date included in time range": [""], "END (EXCLUSIVE)": [""], "End date excluded from time range": [""], "Configure Time Range: Previous...": [""], "Configure Time Range: Last...": [""], "Configure Time Range: Current...": [""], "Configure custom time range": [""], "Relative quantity": [""], "Relative period": [""], "END (INCLUSIVE)": [""], "End date included to time range": [""], "Anchor to": [""], "NOW": [""], "Date/Time": [""], "Return to specific datetime.": [""], "Syntax": [""], "Example": [""], "Moves the given set of dates by a specified interval.": [""], "Truncates the specified date to the accuracy specified by the date unit.": [""], "Get the last date by the date unit.": [""], "Get the specify date for the holiday": [""], "Previous": [""], "Current": [""], "Select date (until included)": [""], "Last day": [""], "Last week": [""], "Last month": [""], "Last quarter": [""], "Last year": [""], "previous calendar week": [""], "previous calendar month": [""], "previous calendar year": [""], "Current day": [""], "Current week": [""], "Current month": [""], "Current quarter": [""], "Current year": [""], "Seconds %s": [""], "Minutes %s": [""], "Hours %s": [""], "Days %s": [""], "Weeks %s": [""], "Months %s": [""], "Quarters %s": [""], "Years %s": [""], "Specific Date/Time": [""], "Relative Date/Time": [""], "Now": [""], "Midnight": [""], "Saved expressions": [""], "Saved": [""], "%s column(s)": [""], "No temporal columns found": [""], "No saved expressions found": [""], "Add calculated temporal columns to dataset in \"Edit datasource\" modal": [""], "Add calculated columns to dataset in \"Edit datasource\" modal": [""], " to mark a column as a time column": [""], " to add calculated columns": [""], "Simple": [""], "Mark a column as temporal in \"Edit datasource\" modal": [""], "Custom SQL": [""], "My column": [""], "This filter might be incompatible with current dataset": [""], "This column might be incompatible with current dataset": [""], "Click to edit label": [""], "Drop columns/metrics here or click": [""], "This metric might be incompatible with current dataset": [""], "\n                This filter was inherited from the dashboard's context.\n                It won't be saved when saving the chart.\n              ": [""], "%s option(s)": [""], "Select subject": [""], "No such column found. To filter on a metric, try the Custom SQL tab.": [""], "To filter on a metric, use Custom SQL tab.": [""], "%s operator(s)": [""], "Select operator": [""], "Comparator option": [""], "Type a value here": [""], "Filter value (case sensitive)": [""], "Failed to retrieve advanced type": [""], "choose WHERE or HAVING...": [""], "Filters by columns": [""], "Filters by metrics": [""], "Fixed": [""], "Based on a metric": [""], "My metric": [""], "Add metric": [""], "Select saved metrics": [""], "%s saved metric(s)": [""], "Saved metric": [""], "No saved metrics found": [""], "Add metrics to dataset in \"Edit datasource\" modal": [""], " to add metrics": [""], "Simple ad-hoc metrics are not enabled for this dataset": [""], "column": [""], "aggregate": [""], "Custom SQL ad-hoc metrics are not enabled for this dataset": [""], "Error while fetching data: %s": [""], "Time series columns": [""], "Actual value": [""], "Sparkline": [""], "Period average": [""], "The column header label": [""], "Column header tooltip": [""], "Type of comparison, value difference or percentage": [""], "Width": [""], "Width of the sparkline": [""], "Height of the sparkline": [""], "Time lag": [""], "Number of periods to compare against. You can use negative numbers to compare from the beginning of the time range.": [""], "Time Lag": [""], "Time ratio": [""], "Number of periods to ratio against": [""], "Time Ratio": [""], "Show Y-axis": [""], "Show Y-axis on the sparkline. Will display the manually set min/max if set or min/max values in the data otherwise.": [""], "Y-axis bounds": [""], "Manually set min/max values for the y-axis.": [""], "Color bounds": [""], "Number bounds used for color encoding from red to blue.\n               Reverse the numbers for blue to red. To get pure red or blue,\n               you can enter either only min or max.": [""], "Optional d3 number format string": [""], "Number format string": [""], "Optional d3 date format string": [""], "Date format string": [""], "Column Configuration": [""], "Select Viz Type": [""], "Currently rendered: %s": [""], "Other": [""], "Search all charts": [""], "No description available.": [""], "Examples": [""], "This visualization type is not supported.": [""], "View all charts": [""], "Select a visualization type": [""], "No results found": [""], "Superset Chart": [""], "New chart": [""], "Edit chart properties": [""], "Export to original .CSV": [""], "Export to .JSON": [""], "Embed code": [""], "Run in SQL Lab": [""], "Code": [""], "Markup type": [""], "Pick your favorite markup language": [""], "Put your code here": [""], "URL parameters": [""], "Extra parameters for use in jinja templated queries": [""], "Annotations and layers": [""], "Annotation layers": [""], "My beautiful colors": [""], "< (Smaller than)": [""], "> (Larger than)": [""], "<= (Smaller or equal)": [""], ">= (Larger or equal)": [""], "== (Is equal)": [""], "!= (Is not equal)": [""], "Not null": [""], "60 days": [""], "90 days": [""], "Send as PDF": [""], "Send as PNG": [""], "Send as CSV": [""], "Send as text": [""], "General information": [""], "Alert condition": [""], "Alert contents": [""], "Report contents": [""], "Notification method": [""], "owners": [""], "content type": [""], "database": [""], "sql": [""], "alert condition": [""], "crontab": [""], "working timeout": [""], "recipients": [""], "email subject": [""], "invalid email": [""], "Not all required fields are complete. Please provide the following:": [""], "Add another notification method": [""], "Add delivery method": [""], "report": [""], "%s updated": [""], "Edit Report": [""], "Edit Alert": [""], "Add Report": [""], "Add Alert": [""], "Add": [""], "Set up basic details, such as name and description.": [""], "Report name": [""], "Alert name": [""], "Enter report name": [""], "Enter alert name": [""], "Include description to be sent with %s": [""], "Report is active": [""], "Alert is active": [""], "Define the database, SQL query, and triggering conditions for alert.": [""], "Select database": [""], "SQL Query": [""], "The result of this query must be a value capable of numeric interpretation e.g. 1, 1.0, or \"1\" (compatible with Python's float() function).": [""], "Trigger Alert If...": [""], "Condition": [""], "Customize data source, filters, and layout.": [""], "Content type": [""], "Select content type": [""], "Select chart to use": [""], "Select dashboard to use": [""], "Content format": [""], "Select format": [""], "Screenshot width": [""], "Input custom width in pixels": [""], "Ignore cache when generating report": [""], "Define delivery schedule, timezone, and frequency settings.": [""], "Timezone": [""], "Log retention": [""], "Working timeout": [""], "Time in seconds": [""], "Grace period": [""], "Choose notification method and recipients.": [""], "Recurring (every)": [""], "CRON Schedule": [""], "Schedule type": [""], "CRON expression": [""], "Report sent": [""], "Alert triggered, notification sent": [""], "Report sending": [""], "Alert running": [""], "Report failed": [""], "Alert failed": [""], "Nothing triggered": [""], "Alert Triggered, In Grace Period": [""], "CC recipients": [""], "BCC recipients": [""], "Email subject name (optional)": [""], "Please enter valid text. Spaces alone are not permitted.": [""], "Private Channels (Bot in channel)": [""], "Notification Method": [""], "Delivery method": [""], "Select Delivery Method": [""], "%s recipients": [""], "Recipients are separated by \",\" or \";\"": [""], "Select channels": [""], "Add CC Recipients": [""], "Add BCC Recipients": [""], "Queries": [""], "No entities have this tag currently assigned": [""], "Add tag to entities": [""], "annotation_layer": [""], "Annotation template updated": [""], "Annotation template created": [""], "Edit annotation layer properties": [""], "Annotation layer name": [""], "Description (this can be seen in the list)": [""], "annotation": [""], "The annotation has been updated": [""], "The annotation has been saved": [""], "Edit annotation": [""], "Add annotation": [""], "date": [""], "Additional information": [""], "Please confirm": [""], "Are you sure you want to delete": [""], "Modified %s": [""], "css_template": [""], "Edit CSS template properties": [""], "Add CSS template": [""], "css": [""], "published": [""], "draft": [""], "SQL Lab": [""], "Adjust how this database will interact with SQL Lab.": [""], "Expose database in SQL Lab": [""], "Allow this database to be queried in SQL Lab": [""], "Allow creation of new tables based on queries": [""], "Allow creation of new views based on queries": [""], "CTAS & CVAS SCHEMA": [""], "Create or select schema...": [""], "Force all tables and views to be created in this schema when clicking CTAS or CVAS in SQL Lab.": [""], "Allow DDL and DML": [""], "Allow the execution of DDL (Data Definition Language: CREATE, DROP, TRUNCATE, etc.) and DML (Data Modification Language: INSERT, UPDATE, DELETE, etc)": [""], "Enable query cost estimation": [""], "For Bigquery, Presto and Postgres, shows a button to compute cost before running a query.": [""], "Allow this database to be explored": [""], "When enabled, users are able to visualize SQL Lab results in Explore.": [""], "Disable SQL Lab data preview queries": [""], "Disable data preview when fetching table metadata in SQL Lab.  Useful to avoid browser performance issues when using  databases with very wide tables.": [""], "Enable row expansion in schemas": [""], "For Trino, describe full schemas of nested ROW types, expanding them with dotted paths": [""], "Performance": [""], "Adjust performance settings of this database.": [""], "Chart cache timeout": [""], "Enter duration in seconds": [""], "Duration (in seconds) of the caching timeout for charts of this database. A timeout of 0 indicates that the cache never expires, and -1 bypasses the cache. Note this defaults to the global timeout if undefined.": [""], "Schema cache timeout": [""], "Duration (in seconds) of the metadata caching timeout for schemas of this database. If left unset, the cache never expires.": [""], "Table cache timeout": [""], "Duration (in seconds) of the metadata caching timeout for tables of this database. If left unset, the cache never expires. ": [""], "Asynchronous query execution": [""], "This option has been disabled by the administrator.": [""], "Cancel query on window unload event": [""], "Terminate running queries when browser window closed or navigated to another page. Available for Presto, Hive, MySQL, Postgres and Snowflake databases.": [""], "Security": [""], "Add extra connection information.": [""], "Secure extra": [""], "JSON string containing additional connection configuration. This is used to provide connection information for systems like Hive, Presto and BigQuery which do not conform to the username:password syntax normally used by SQLAlchemy.": [""], "Enter CA_BUNDLE": [""], "Optional CA_BUNDLE contents to validate HTTPS requests. Only available on certain database engines.": [""], "Impersonate logged in user (Presto, Trino, Drill, Hive, and GSheets)": [""], "If Presto or Trino, all the queries in SQL Lab are going to be executed as the currently logged on user who must have permission to run them. If Hive and hive.server2.enable.doAs is enabled, will run the queries as service account, but impersonate the currently logged on user via hive.server2.proxy.user property.": [""], "Allow file uploads to database": [""], "Schemas allowed for File upload": [""], "A comma-separated list of schemas that files are allowed to upload to.": [""], "Additional settings.": [""], "Metadata Parameters": [""], "The metadata_params object gets unpacked into the sqlalchemy.MetaData call.": [""], "Engine Parameters": [""], "The engine_params object gets unpacked into the sqlalchemy.create_engine call.": [""], "Version": [""], "Version number": [""], "Specify the database version. This is used with Presto for query cost estimation, and Dremio for syntax changes, among others.": [""], "Disable drill to detail": [""], "Disables the drill to detail feature for this database.": [""], "Allow changing catalogs": [""], "Give access to multiple catalogs in a single database connection.": [""], "STEP %(stepCurr)s OF %(stepLast)s": [""], "Enter Primary Credentials": [""], "Need help? Learn how to connect your database": [""], "Database connected": [""], "Create a dataset to begin visualizing your data as a chart or go to\n          SQL Lab to query your data.": [""], "Enter the required %(dbModelName)s credentials": [""], "Need help? Learn more about": [""], "connecting to %(dbModelName)s": [""], "Select a database to connect": [""], "SSH Host": [""], "e.g. 127.0.0.1": [""], "SSH Port": [""], "e.g. Analytics": [""], "Login with": [""], "Password": [""], "Private Key & Password": [""], "SSH Password": [""], "e.g. ********": [""], "Private Key": [""], "Paste Private Key here": [""], "Private Key Password": [""], "SSH Tunnel": [""], "SSH Tunnel configuration parameters": [""], "Display Name": [""], "Name your database": [""], "Pick a name to help you identify this database.": [""], "dialect+driver://username:password@host:port/database": [""], "Refer to the": [""], "for more information on how to structure your URI.": [""], "Test connection": [""], "Please enter a SQLAlchemy URI to test": [""], "e.g. world_population": [""], "Connection failed, please check your connection settings.": [""], "Database settings updated": [""], "Sorry there was an error fetching database information: %s": [""], "Or choose from a list of other databases we support:": [""], "Supported databases": [""], "Choose a database...": [""], "Want to add a new database?": [""], "Any databases that allow connections via SQL Alchemy URIs can be added. ": [""], "Any databases that allow connections via SQL Alchemy URIs can be added. Learn about how to connect a database driver ": [""], "Connect": [""], "Finish": [""], "This database is managed externally, and can't be edited in Superset": [""], "The passwords for the databases below are needed in order to import them. Please note that the \"Secure Extra\" and \"Certificate\" sections of the database configuration are not present in explore files and should be added manually after the import if they are needed.": [""], "You are importing one or more databases that already exist. Overwriting might cause you to lose some of your work. Are you sure you want to overwrite?": [""], "Database Creation Error": [""], "We are unable to connect to your database. Click \"See more\" for database-provided information that may help troubleshoot the issue.": [""], "CREATE DATASET": [""], "QUERY DATA IN SQL LAB": [""], "Connect a database": [""], "Edit database": [""], "Connect this database using the dynamic form instead": [""], "Click this link to switch to an alternate form that exposes only the required fields needed to connect this database.": [""], "Additional fields may be required": [""], "Select databases require additional fields to be completed in the Advanced tab to successfully connect the database. Learn what requirements your databases has ": [""], "Import database from file": [""], "Connect this database with a SQLAlchemy URI string instead": [""], "Click this link to switch to an alternate form that allows you to input the SQLAlchemy URL for this database manually.": [""], "This can be either an IP address (e.g. 127.0.0.1) or a domain name (e.g. mydatabase.com).": [""], "Host": [""], "e.g. 5432": [""], "Port": [""], "e.g. sql/protocolv1/o/12345": [""], "Copy the name of the HTTP Path of your cluster.": [""], "Database name": [""], "Copy the name of the database you are trying to connect to.": [""], "e.g. hive_metastore": [""], "Default Catalog": [""], "The default catalog that should be used for the connection.": [""], "e.g. default": [""], "Default Schema": [""], "The default schema that should be used for the connection.": [""], "Paste your access token here": [""], "Access token": [""], "Pick a nickname for how the database will display in Superset.": [""], "e.g. param1=value1&param2=value2": [""], "Additional Parameters": [""], "Add additional custom parameters": [""], "SSL Mode \"require\" will be used.": [""], "Type of Google Sheets allowed": [""], "Publicly shared sheets only": [""], "Public and privately shared sheets": [""], "How do you want to enter service account credentials?": [""], "Upload JSON file": [""], "Copy and Paste JSON credentials": [""], "Service Account": [""], "Paste content of service credentials JSON file here": [""], "Copy and paste the entire service account .json file here": [""], "Upload Credentials": [""], "Use the JSON file you automatically downloaded when creating your service account.": [""], "Connect Google Sheets as tables to this database": [""], "Google Sheet Name and URL": [""], "Enter a name for this sheet": [""], "Paste the shareable Google Sheet URL here": [""], "Add sheet": [""], "Copy the identifier of the account you are trying to connect to.": [""], "e.g. xy12345.us-east-2.aws": [""], "e.g. compute_wh": [""], "e.g. AccountAdmin": [""], "Upload file to preview columns": [""], "Data Imported": [""], "Uploading a file is required": [""], "Upload a file with a valid extension. Valid: [%s]": [""], "Selecting a database is required": [""], "CSV Upload": [""], "Excel Upload": [""], "Columnar Upload": [""], "Upload a file to a database.": [""], "%(type)s File": [""], "Preview uploaded file": [""], "Select a database to upload the file to": [""], "Select a schema": [""], "Select a schema if the database supports this": [""], "Name of table to be created": [""], "Delimiter": [""], "Select a delimiter for this data": [""], "Choose a delimiter": [""], "Sheet name": [""], "Choose sheet name": [""], "Select a sheet name from the uploaded file": [""], "File Settings": [""], "Adjust how spaces, blank lines, null values are handled and other file wide settings.": [""], "If Table Already Exists": [""], "What should happen if the table already exists": [""], "Choose already exists": [""], "Columns To Be Parsed as Dates": [""], "Choose columns to be parsed as dates": [""], "A comma separated list of columns that should be parsed as dates": [""], "Decimal Character": [""], "Character to interpret as decimal point": [""], "Null Values": [""], "Choose values that should be treated as null. Warning: Hive database supports only a single value": [""], "Skip spaces after delimiter": [""], "Skip blank lines rather than interpreting them as Not A Number values": [""], "DD/MM format dates, international and European format": [""], "Adjust column settings such as specifying the columns to read, how duplicates are handled, column data types, and more.": [""], "Columns To Read": [""], "Choose columns to read": [""], "List of the column names that should be read": [""], "Column Data Types": [""], "A dictionary with column names and their data types if you need to change the defaults. Example: {\"user_id\":\"int\"}. Check Python's Pandas library for supported data types.": [""], "Column data types": [""], "Create dataframe index": [""], "Index Column": [""], "Column to use as the index of the dataframe. If None is given, Index label is used.": [""], "Choose index column": [""], "Index Label": [""], "Label for the index column. Don't use an existing column name.": [""], "Index label": [""], "Set header rows and the number of rows to read or skip.": [""], "Header Row": [""], "Row containing the headers to use as column names (0 is first line of data).": [""], "Header row": [""], "Rows to Read": [""], "Number of rows of file to read. Leave empty (default) to read all rows": [""], "Rows to read": [""], "Skip Rows": [""], "Number of rows to skip at start of file.": [""], "Skip rows": [""], "Duplicate dataset": [""], "Duplicate": [""], "New dataset name": [""], "The passwords for the databases below are needed in order to import them together with the datasets. Please note that the \"Secure Extra\" and \"Certificate\" sections of the database configuration are not present in export files, and should be added manually after the import if they are needed.": [""], "You are importing one or more datasets that already exist. Overwriting might cause you to lose some of your work. Are you sure you want to overwrite?": [""], "Refreshing columns": [""], "Table columns": [""], "Loading": [""], "This table already has a dataset associated with it. You can only associate one dataset with a table.\n": [""], "View Dataset": [""], "This table already has a dataset": [""], "Datasets can be created from database tables or SQL queries. Select a database table to the left or ": [""], "create dataset from SQL query": [""], " to open SQL Lab. From there you can save the query as a dataset.": [""], "Select dataset source": [""], "No table columns": [""], "This database table does not contain any data. Please select a different table.": [""], "An Error Occurred": [""], "Unable to load columns for the selected table. Please select a different table.": [""], "The API response from %s does not match the IDatabaseTable interface.": [""], "Usage": [""], "Chart owners": [""], "Chart last modified": [""], "Chart last modified by": [""], "Dashboard usage": [""], "Create chart with dataset": [""], "chart": [""], "No charts": [""], "This dataset is not used to power any charts.": [""], "Select a database table.": [""], "Create dataset and create chart": [""], "New dataset": [""], "Select a database table and create dataset": [""], "dataset name": [""], "Not defined": [""], "There was an error fetching dataset": [""], "There was an error fetching dataset's related objects": [""], "There was an error loading the dataset metadata": [""], "[Untitled]": [""], "Unknown": [""], "Viewed %s": [""], "Edited": [""], "Created": [""], "Viewed": [""], "Favorite": [""], "Mine": [""], "View All »": [""], "dashboard": [""], "An error occurred while fetching dashboards: %s": [""], "charts": [""], "dashboards": [""], "recents": [""], "saved queries": [""], "No charts yet": [""], "No dashboards yet": [""], "No recents yet": [""], "No saved queries yet": [""], "%(other)s charts will appear here": [""], "%(other)s dashboards will appear here": [""], "%(other)s recents will appear here": [""], "%(other)s saved queries will appear here": [""], "Recently viewed charts, dashboards, and saved queries will appear here": [""], "Recently created charts, dashboards, and saved queries will appear here": [""], "Recently edited charts, dashboards, and saved queries will appear here": [""], "SQL query": [""], "You don't have any favorites yet!": [""], "See all %(tableName)s": [""], "Connect database": [""], "Create dataset": [""], "Connect Google Sheet": [""], "Upload CSV to database": [""], "Upload Excel to database": [""], "Upload Columnar file to database": [""], "Enable 'Allow file uploads to database' in any database's settings": [""], "Onboarding": [""], "Info": [""], "Logout": [""], "About": [""], "Powered by Apache Superset": [""], "SHA": [""], "Build": [""], "Documentation": [""], "Report a bug": [""], "Login": [""], "query": [""], "Deleted: %s": [""], "There was an issue deleting %s: %s": [""], "This action will permanently delete the saved query.": [""], "Delete Query?": [""], "Saved queries": [""], "Tab name": [""], "User query": [""], "Executed query": [""], "Query name": [""], "SQL Copied!": [""], "Sorry, your browser does not support copying.": [""], "There was an issue fetching reports attached to this dashboard.": [""], "The report has been created": [""], "Report updated": [""], "We were unable to active or deactivate this report.": [""], "Your report could not be deleted": [""], "Weekly Report for %s": [""], "Weekly Report": [""], "Edit email report": [""], "Schedule a new email report": [""], "Message content": [""], "Text embedded in email": [""], "Image (PNG) embedded in email": [""], "Formatted CSV attached in email": [""], "Report Name": [""], "Include a description that will be sent with your report": [""], "The report will be sent to your email at": [""], "Failed to update report": [""], "Failed to create report": [""], "Set up an email report": [""], "Email reports active": [""], "Delete email report": [""], "Schedule email report": [""], "This action will permanently delete %s.": [""], "Delete Report?": [""], "rowlevelsecurity": [""], "Rule added": [""], "Edit Rule": [""], "Add Rule": [""], "Rule Name": [""], "The name of the rule must be unique": [""], "Regular filters add where clauses to queries if a user belongs to a role referenced in the filter, base filters apply filters to all queries except the roles defined in the filter, and can be used to define what users can see if no RLS filters within a filter group apply to them.": [""], "Datasets": [""], "These are the datasets this filter will be applied to.": [""], "Excluded roles": [""], "For regular filters, these are the roles this filter will be applied to. For base filters, these are the roles that the filter DOES NOT apply to, e.g. Admin if admin should see all data.": [""], "Group Key": [""], "Filters with the same group key will be ORed together within the group, while different filter groups will be ANDed together. Undefined group keys are treated as unique groups, i.e. are not grouped together. For example, if a table has three filters, of which two are for departments Finance and Marketing (group key = 'department'), and one refers to the region Europe (group key = 'region'), the filter clause would apply the filter (department = 'Finance' OR department = 'Marketing') AND (region = 'Europe').": [""], "Clause": [""], "This is the condition that will be added to the WHERE clause. For example, to only return rows for a particular client, you might define a regular filter with the clause `client_id = 9`. To display no rows unless a user belongs to a RLS filter role, a base filter can be created with the clause `1 = 0` (always false).": [""], "Regular": [""], "Base": [""], "%s items could not be tagged because you don’t have edit permissions to all selected objects.": [""], "Tagged %s %ss": [""], "Failed to tag items": [""], "Bulk tag": [""], "You are adding tags to %s %ss": [""], "tags": [""], "Select Tags": [""], "Tag updated": [""], "Tag created": [""], "Tag name": [""], "Name of your tag": [""], "Add description of your tag": [""], "Chosen non-numeric column": [""], "UI Configuration": [""], "Filter value is required": [""], "User must select a value before applying the filter": [""], "Single value": [""], "Use only a single value.": [""], "Range filter plugin using AntD": [""], "Experimental": [""], " (excluded)": [""], "Check for sorting ascending": [""], "Can select multiple values": [""], "Select first filter value by default": [""], "When using this option, default value can’t be set": [""], "Inverse selection": [""], "Exclude selected values": [""], "Dynamically search all filter values": [""], "By default, each filter loads at most 1000 choices at the initial page load. Check this box if you have more than 1000 filter values and want to enable dynamically searching that loads filter values as users type (may add stress to your database).": [""], "Select by id with translation filter plugin using AntD'": [""], "Select by id filter plugin using AntD'": [""], "Select with translation filter plugin using AntD'": [""], "Select filter plugin using AntD": [""], "Custom time filter plugin": [""], "No time columns": [""], "Time column filter plugin": [""], "Time grain filter plugin": [""], "Working": [""], "Not triggered": [""], "On Grace": [""], "Alert": [""], "reports": [""], "alerts": [""], "There was an issue deleting the selected %s: %s": [""], "Last run": [""], "Active": [""], "Execution log": [""], "Bulk select": [""], "No %s yet": [""], "Owner": [""], "An error occurred while fetching owners values: %s": [""], "Status": [""], "An error occurred while fetching dataset datasource values: %s": [""], "Alerts & reports": [""], "Alerts": [""], "Reports": [""], "Delete %s?": [""], "Are you sure you want to delete the selected %s?": [""], "Error Fetching Tagged Objects": [""], "Edit Tag": [""], "There was an issue deleting the selected layers: %s": [""], "Edit template": [""], "Delete template": [""], "Changed by": [""], "No annotation layers yet": [""], "This action will permanently delete the layer.": [""], "Delete Layer?": [""], "Are you sure you want to delete the selected layers?": [""], "There was an issue deleting the selected annotations: %s": [""], "Delete annotation": [""], "Annotation": [""], "No annotation yet": [""], "Back to all": [""], "Are you sure you want to delete %s?": [""], "Delete Annotation?": [""], "Are you sure you want to delete the selected annotations?": [""], "Failed to load chart data": [""], "view instructions": [""], "Add a dataset": [""], "Choose a dataset": [""], "Choose chart type": [""], "Please select both a Dataset and a Chart type to proceed": [""], "The passwords for the databases below are needed in order to import them together with the charts. Please note that the \"Secure Extra\" and \"Certificate\" sections of the database configuration are not present in export files, and should be added manually after the import if they are needed.": [""], "You are importing one or more charts that already exist. Overwriting might cause you to lose some of your work. Are you sure you want to overwrite?": [""], "Chart imported": [""], "There was an issue deleting the selected charts: %s": [""], "An error occurred while fetching dashboards": [""], "Tag": [""], "An error occurred while fetching chart owners values: %s": [""], "Certified": [""], "Alphabetical": [""], "Recently modified": [""], "Least recently modified": [""], "Import charts": [""], "Are you sure you want to delete the selected charts?": [""], "CSS templates": [""], "There was an issue deleting the selected templates: %s": [""], "CSS template": [""], "This action will permanently delete the template.": [""], "Delete Template?": [""], "Are you sure you want to delete the selected templates?": [""], "The passwords for the databases below are needed in order to import them together with the dashboards. Please note that the \"Secure Extra\" and \"Certificate\" sections of the database configuration are not present in export files, and should be added manually after the import if they are needed.": [""], "You are importing one or more dashboards that already exist. Overwriting might cause you to lose some of your work. Are you sure you want to overwrite?": [""], "Dashboard imported": [""], "There was an issue deleting the selected dashboards: ": [""], "An error occurred while fetching dashboard owner values: %s": [""], "Are you sure you want to delete the selected dashboards?": [""], "An error occurred while fetching database related data: %s": [""], "Upload file to database": [""], "Upload CSV": [""], "Upload Excel": [""], "Upload Columnar": [""], "AQE": [""], "Allow data manipulation language": [""], "DML": [""], "File upload": [""], "Delete database": [""], "The database %s is linked to %s charts that appear on %s dashboards and users have %s SQL Lab tabs using this database open. Are you sure you want to continue? Deleting the database will break those objects.": [""], "Delete Database?": [""], "Dataset imported": [""], "An error occurred while fetching dataset related data": [""], "An error occurred while fetching dataset related data: %s": [""], "Physical dataset": [""], "Virtual dataset": [""], "Virtual": [""], "Search by query text": [""], "An error occurred while fetching datasets: %s": [""], "An error occurred while fetching schema values: %s": [""], "An error occurred while fetching dataset owner values: %s": [""], "Import datasets": [""], "There was an issue deleting the selected datasets: %s": [""], "There was an issue duplicating the dataset.": [""], "There was an issue duplicating the selected datasets: %s": [""], "The dataset %s is linked to": [""], "%s charts": [""], "that appear on": [""], "%s dashboards.": [""], "Are you sure you want to continue? Deleting the dataset will break those objects.": [""], "The dataset %s is linked to %s charts that appear on %s dashboards. Are you sure you want to continue? Deleting the dataset will break those objects.": [""], "It will also affect": [""], "the dashboard filters": [""], "tied to this dataset if any.": [""], "Delete Dataset?": [""], "Are you sure you want to delete the selected datasets?": [""], "0 Selected": [""], "%s Selected (Virtual)": [""], "%s Selected (Physical)": [""], "%s Selected (%s Physical, %s Virtual)": [""], "log": [""], "Execution ID": [""], "Scheduled at (UTC)": [""], "Start at (UTC)": [""], "Error message": [""], "There was an issue fetching your recent activity: %s": [""], "Home": [""], "Thumbnails": [""], "Recents": [""], "There was an issue previewing the selected query. %s": [""], "TABLES": [""], "Open query in SQL Lab": [""], "An error occurred while fetching database values: %s": [""], "An error occurred while fetching user values: %s": [""], "Row Level Security": [""], "Deleted %s": [""], "There was an issue deleting rules: %s": [""], "No Rules yet": [""], "Are you sure you want to delete the selected rules?": [""], "The passwords for the databases below are needed in order to import them together with the saved queries. Please note that the \"Secure Extra\" and \"Certificate\" sections of the database configuration are not present in export files, and should be added manually after the import if they are needed.": [""], "You are importing one or more saved queries that already exist. Overwriting might cause you to lose some of your work. Are you sure you want to overwrite?": [""], "Query imported": [""], "There was an issue previewing the selected query %s": [""], "Import queries": [""], "Link Copied!": [""], "There was an issue deleting the selected queries: %s": [""], "Edit query": [""], "Copy query URL": [""], "Export query": [""], "Delete query": [""], "Searches all text fields: Name, Description, Database & Schema": [""], "Are you sure you want to delete the selected queries?": [""], "queries": [""], "tag": [""], "No Tags created": [""], "Are you sure you want to delete the selected tags?": [""], "Image download failed, please refresh and try again.": [""], "PDF download failed, please refresh and try again.": [""], "Select values in highlighted field(s) in the control panel. Then run the query by clicking on the %s button.": [""], "An error occurred while fetching %s info: %s": [""], "An error occurred while fetching %ss: %s": [""], "An error occurred while creating %ss: %s": [""], "Please re-export your file and try importing again": [""], "An error occurred while importing %s: %s": [""], "There was an error fetching the favorite status: %s": [""], "There was an error saving the favorite status: %s": [""], "Connection looks good!": [""], "ERROR: %s": [""], "There was an error fetching the filtered charts and dashboards:": [""], "There was an issue deleting: %s": [""], "URL": [""], "Templated link, it's possible to include {{ metric }} or other values coming from the controls.": [""], "Time-series Table": [""], "Compare multiple time series charts (as sparklines) and related metrics quickly.": [""], "Text": [""], "We have the following keys: %s": [""], "Query History": [""], "Manage": [""], "List Users": [""], "List Roles": [""], "User Registrations": [""], "Action Log": [""], "Database Connections": [""], "Plugins": [""], "CSS Templates": [""], "Alerts & Reports": [""], "Drop columns here or click": [""], "Saved Queries": [""], "Select All": [""], "Day": [""], "Week": [""], "Month": [""], "Quarter": [""], "Year": [""], "Week starting sunday": [""], "Week starting monday": [""], "Week ending saturday": [""], "Week_ending sunday": [""], "Week starting Sunday": [""], "Week starting Monday": [""], "Week ending Saturday": [""], "Managing Company": [""], "Create data": [""], "Vizualize data": [""], "center": [""], "oldest": [""], "average": [""], "latest": [""], "changes": ["change"], "vs": [""]}}}